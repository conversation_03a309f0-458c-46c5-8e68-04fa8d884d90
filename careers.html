<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Careers | Manufacturing Jobs in Navi Mumbai & Pune - SUYASH</title>

    <!-- SEO Meta Tags -->
    <meta name="description" content="Join SUYASH Manufacturing team. Career opportunities in precision engineering, quality control, production supervision. Jobs available in Navi Mumbai and Pune facilities.">
    <meta name="keywords" content="manufacturing jobs, engineering careers, quality control jobs, production supervisor, Navi Mumbai jobs, Pune jobs, precision engineering careers, manufacturing employment">
    <meta name="author" content="SUYASH Manufacturing">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="https://www.suyashmanufacturing.com/careers.html">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://www.suyashmanufacturing.com/careers.html">
    <meta property="og:title" content="Careers | Manufacturing Jobs in Navi Mumbai & Pune - SUYASH">
    <meta property="og:description" content="Join SUYASH Manufacturing team. Career opportunities in precision engineering, quality control, production supervision.">
    <meta property="og:image" content="https://www.suyashmanufacturing.com/images/logo.png">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary">
    <meta property="twitter:url" content="https://www.suyashmanufacturing.com/careers.html">
    <meta property="twitter:title" content="Careers | Manufacturing Jobs in Navi Mumbai & Pune - SUYASH">
    <meta property="twitter:description" content="Join SUYASH Manufacturing team. Career opportunities in precision engineering, quality control, production supervision.">
    <meta property="twitter:image" content="https://www.suyashmanufacturing.com/images/logo.png">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Custom Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#f26722',
                        secondary: '#0a0a0a',
                        accent: '#e05310',
                        gray: {
                            50: '#f9fafb',
                            100: '#f3f4f6',
                            200: '#e5e7eb',
                            300: '#d1d5db',
                            400: '#9ca3af',
                            500: '#6b7280',
                            600: '#4b5563',
                            700: '#374151',
                            800: '#1f2937',
                            900: '#111827',
                        }
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                        'display': ['Poppins', 'system-ui', 'sans-serif'],
                    },
                    animation: {
                        'fade-in-up': 'fadeInUp 0.6s ease-out',
                        'fade-in-down': 'fadeInDown 0.6s ease-out',
                        'fade-in-left': 'fadeInLeft 0.6s ease-out',
                        'fade-in-right': 'fadeInRight 0.6s ease-out',
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'bounce-slow': 'bounce 2s infinite',
                    },
                    keyframes: {
                        fadeInUp: {
                            '0%': { opacity: '0', transform: 'translateY(30px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        },
                        fadeInDown: {
                            '0%': { opacity: '0', transform: 'translateY(-30px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        },
                        fadeInLeft: {
                            '0%': { opacity: '0', transform: 'translateX(-30px)' },
                            '100%': { opacity: '1', transform: 'translateX(0)' }
                        },
                        fadeInRight: {
                            '0%': { opacity: '0', transform: 'translateX(30px)' },
                            '100%': { opacity: '1', transform: 'translateX(0)' }
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="font-sans text-gray-900 bg-white overflow-x-hidden">
    <!-- Loading Screen -->
    <div id="loader" class="fixed inset-0 z-50 flex items-center justify-center bg-white transition-opacity duration-500">
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-4 border-primary border-t-transparent"></div>
            <p class="mt-4 text-gray-600 font-medium">Loading Excellence...</p>
        </div>
    </div>

    <!-- Navigation Header -->
    <header id="header" class="fixed top-0 left-0 right-0 z-40 bg-white/95 backdrop-blur-md border-b border-gray-100 transition-all duration-300">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-20">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <a href="index.html" class="flex items-center space-x-3">
                        <img src="images/logo.png" alt="SUYASH" class="h-12 w-auto transition-transform duration-300 hover:scale-105" />
                        
                    </a>
                </div>
                
                <!-- Desktop Navigation -->
                <nav class="hidden lg:flex items-center space-x-8">
                    <a href="index.html" class="text-gray-700 hover:text-primary transition-colors duration-200 font-medium">Home</a>
                    <div class="relative group">
                        <button class="flex items-center space-x-1 text-gray-700 hover:text-primary transition-colors duration-200 font-medium">
                            <span>About</span>
                            <svg class="w-4 h-4 transition-transform duration-200 group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div class="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-100 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                            <div class="py-2">
                                <a href="history.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors duration-200">History</a>
                                <a href="locations.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors duration-200">Locations</a>
                                <a href="csr-activities.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors duration-200">CSR Activities</a>
                            </div>
                        </div>
                    </div>
                    <a href="services.html" class="text-gray-700 hover:text-primary transition-colors duration-200 font-medium">Services</a>
                    <a href="quality.html" class="text-gray-700 hover:text-primary transition-colors duration-200 font-medium">Quality</a>
                    <a href="careers.html" class="text-primary font-semibold border-b-2 border-primary pb-1">Careers</a>
                    <a href="events.html" class="text-gray-700 hover:text-primary transition-colors duration-200 font-medium">Events</a>
                    <a href="contact-us.html" class="bg-primary text-white px-6 py-2 rounded-full hover:bg-accent transition-colors duration-200 font-medium">Contact Us</a>
                </nav>
                
                <!-- Mobile Menu Button -->
                <button id="mobileMenuToggle" class="lg:hidden flex flex-col items-center justify-center w-8 h-8 space-y-1.5">
                    <span class="w-6 h-0.5 bg-gray-700 transition-all duration-300"></span>
                    <span class="w-6 h-0.5 bg-gray-700 transition-all duration-300"></span>
                    <span class="w-6 h-0.5 bg-gray-700 transition-all duration-300"></span>
                </button>
            </div>
        </div>
        
        <!-- Mobile Navigation Menu -->
        <div id="mobileMenu" class="lg:hidden fixed inset-x-0 top-20 bg-white border-b border-gray-100 shadow-lg transform -translate-y-full opacity-0 transition-all duration-300">
            <div class="px-4 py-6 space-y-4">
                <a href="index.html" class="block text-gray-700 hover:text-primary transition-colors duration-200">Home</a>
                <div class="space-y-2">
                    <p class="text-gray-900 font-medium">About</p>
                    <div class="pl-4 space-y-2">
                        <a href="history.html" class="block text-gray-600 hover:text-primary transition-colors duration-200">History</a>
                        <a href="locations.html" class="block text-gray-600 hover:text-primary transition-colors duration-200">Locations</a>
                        <a href="csr-activities.html" class="block text-gray-600 hover:text-primary transition-colors duration-200">CSR Activities</a>
                    </div>
                </div>
                <a href="services.html" class="block text-gray-700 hover:text-primary transition-colors duration-200">Services</a>
                <a href="quality.html" class="block text-gray-700 hover:text-primary transition-colors duration-200">Quality</a>
                <a href="careers.html" class="block text-primary font-semibold">Careers</a>
                <a href="events.html" class="block text-gray-700 hover:text-primary transition-colors duration-200">Events</a>
                <a href="contact-us.html" class="block bg-primary text-white px-6 py-3 rounded-full text-center hover:bg-accent transition-colors duration-200">Contact Us</a>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="relative bg-gradient-to-br from-gray-900 to-gray-800 text-white pt-32 pb-20">
        <div class="absolute inset-0 bg-black/20"></div>
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-5xl lg:text-6xl font-bold mb-6">
                <span class="text-primary">Careers</span> at SUYASH
            </h1>
            <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                Join our team of dedicated professionals and build your career in manufacturing excellence
            </p>
        </div>
    </section>

    <!-- Careers Content Section -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="max-w-4xl mx-auto">
                <div class="text-center mb-16">
                    <span class="inline-block px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-semibold mb-4">
                        Join Our Team
                    </span>
                    <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">Build Your Career With Us</h2>
                    <p class="text-lg text-gray-600 leading-relaxed">
                        At SUYASH, we believe our people are our greatest asset. We offer exciting career opportunities in a dynamic manufacturing environment where innovation, quality, and excellence are at the forefront of everything we do.
                    </p>
                </div>
                
                <!-- Why Work With Us -->
                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
                    <div class="bg-gray-50 rounded-2xl p-6 text-center hover:shadow-lg transition-shadow duration-300">
                        <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-2">Innovation</h3>
                        <p class="text-gray-600">Work with cutting-edge technology and innovative manufacturing processes</p>
                    </div>
                    
                    <div class="bg-gray-50 rounded-2xl p-6 text-center hover:shadow-lg transition-shadow duration-300">
                        <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-2">Growth</h3>
                        <p class="text-gray-600">Continuous learning and professional development opportunities</p>
                    </div>
                    
                    <div class="bg-gray-50 rounded-2xl p-6 text-center hover:shadow-lg transition-shadow duration-300">
                        <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-2">Team Culture</h3>
                        <p class="text-gray-600">Collaborative environment with supportive team members</p>
                    </div>
                </div>
                
                <!-- Current Openings -->
                <div class="bg-white rounded-2xl shadow-lg border border-gray-100 p-8 lg:p-12 mb-12">
                    <h3 class="text-2xl font-bold text-gray-900 mb-6">Current Opportunities</h3>
                    <div class="space-y-6">
                        <div class="border border-gray-200 rounded-lg p-6 hover:border-primary transition-colors duration-300">
                            <div class="flex flex-col md:flex-row md:items-center justify-between">
                                <div>
                                    <h4 class="text-lg font-semibold text-gray-900 mb-2">Manufacturing Engineer</h4>
                                    <p class="text-gray-600 mb-2">Design and optimize manufacturing processes for precision components</p>
                                    <div class="flex flex-wrap gap-2">
                                        <span class="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm">Full-time</span>
                                        <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">Engineering</span>
                                        <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">Navi Mumbai</span>
                                    </div>
                                </div>
                                <div class="mt-4 md:mt-0">
                                    <a href="contact-us.html" class="inline-flex items-center bg-primary text-white px-6 py-2 rounded-full hover:bg-accent transition-colors duration-200">
                                        Apply Now
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <div class="border border-gray-200 rounded-lg p-6 hover:border-primary transition-colors duration-300">
                            <div class="flex flex-col md:flex-row md:items-center justify-between">
                                <div>
                                    <h4 class="text-lg font-semibold text-gray-900 mb-2">Quality Control Specialist</h4>
                                    <p class="text-gray-600 mb-2">Ensure product quality through rigorous testing and inspection processes</p>
                                    <div class="flex flex-wrap gap-2">
                                        <span class="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm">Full-time</span>
                                        <span class="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm">Quality</span>
                                        <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">Pune</span>
                                    </div>
                                </div>
                                <div class="mt-4 md:mt-0">
                                    <a href="contact-us.html" class="inline-flex items-center bg-primary text-white px-6 py-2 rounded-full hover:bg-accent transition-colors duration-200">
                                        Apply Now
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <div class="border border-gray-200 rounded-lg p-6 hover:border-primary transition-colors duration-300">
                            <div class="flex flex-col md:flex-row md:items-center justify-between">
                                <div>
                                    <h4 class="text-lg font-semibold text-gray-900 mb-2">Production Supervisor</h4>
                                    <p class="text-gray-600 mb-2">Lead production teams and ensure efficient manufacturing operations</p>
                                    <div class="flex flex-wrap gap-2">
                                        <span class="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm">Full-time</span>
                                        <span class="px-3 py-1 bg-orange-100 text-orange-800 rounded-full text-sm">Operations</span>
                                        <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">Multiple Locations</span>
                                    </div>
                                </div>
                                <div class="mt-4 md:mt-0">
                                    <a href="contact-us.html" class="inline-flex items-center bg-primary text-white px-6 py-2 rounded-full hover:bg-accent transition-colors duration-200">
                                        Apply Now
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Call to Action -->
                <div class="bg-gradient-to-br from-primary to-accent rounded-2xl p-8 text-white text-center">
                    <div class="max-w-2xl mx-auto">
                        <h3 class="text-2xl font-bold mb-4">Ready to Join Our Team?</h3>
                        <p class="text-lg text-white/90 mb-6">
                            Don't see a position that matches your skills? Send us your resume and we'll keep you in mind for future opportunities.
                        </p>
                        <a href="contact-us.html" class="inline-flex items-center bg-white text-primary px-8 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors duration-300">
                            Submit Resume
                            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gradient-to-br from-gray-900 to-black text-white">
        <!-- Main Footer Content -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <div class="grid lg:grid-cols-4 md:grid-cols-2 gap-8">
                <!-- Company Info -->
                <div class="lg:col-span-2">
                    <div class="flex items-center space-x-3 mb-6">
                        <img src="images/logo.png" alt="SUYASH" class="h-12 w-auto" />

                    </div>
                    <p class="text-gray-300 leading-relaxed mb-6 max-w-md">
                        Leading manufacturer of precision engineering components, serving global markets with innovative solutions and uncompromising quality.
                    </p>
                    <div class="flex space-x-4">
                        <!-- <a href="#" class="w-10 h-10 bg-primary/20 hover:bg-primary rounded-full flex items-center justify-center transition-colors duration-300">
                            <img src="images/f-icon.png" alt="Facebook" class="w-5 h-5">
                        </a> -->
                        <a href="https://www.linkedin.com/company/suyash-impex-pvt-ltd/" class="w-10 h-10 bg-primary/20 hover:bg-primary rounded-full flex items-center justify-center transition-colors duration-300">
                            <img src="images/in-icon.png" alt="LinkedIn" class="w-5 h-5">
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="text-lg font-semibold mb-6">Quick Links</h4>
                    <ul class="space-y-3">
                        <li><a href="index.html" class="text-gray-300 hover:text-primary transition-colors duration-200">Home</a></li>
                        <li><a href="history.html" class="text-gray-300 hover:text-primary transition-colors duration-200">History</a></li>
                        <li><a href="services.html" class="text-gray-300 hover:text-primary transition-colors duration-200">Services</a></li>
                        <li><a href="quality.html" class="text-gray-300 hover:text-primary transition-colors duration-200">Quality</a></li>
                        <li><a href="careers.html" class="text-primary font-medium">Careers</a></li>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div>
                    <h4 class="text-lg font-semibold mb-6">Get In Touch</h4>
                    <ul class="space-y-3">
                        <li><a href="events.html" class="text-gray-300 hover:text-primary transition-colors duration-200">Events</a></li>
                        <li><a href="contact-us.html" class="text-gray-300 hover:text-primary transition-colors duration-200">Contact Us</a></li>
                        <li><a href="locations.html" class="text-gray-300 hover:text-primary transition-colors duration-200">Our Locations</a></li>
                        <li><a href="csr-activities.html" class="text-gray-300 hover:text-primary transition-colors duration-200">CSR Activities</a></li>
                    </ul>

                    <!-- CTA Button -->
                    <div class="mt-6">
                        <a href="contact-us.html" class="inline-flex items-center bg-primary hover:bg-accent text-white px-6 py-3 rounded-full font-semibold transition-all duration-300 transform hover:scale-105">
                            Start Your Project
                            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer Bottom -->
        <div class="border-t border-gray-800">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-gray-400 text-sm">
                        All Right Reserved Suyash 2015 | Developed by : <a href="#" class="text-primary hover:text-accent transition-colors duration-200">SV</a>
                    </p>
                    <div class="flex items-center space-x-6 mt-4 md:mt-0">
                        <span class="text-gray-400 text-sm">Follow us</span>
                        <div class="flex space-x-3">
                            <!-- <a href="#" class="text-gray-400 hover:text-primary transition-colors duration-200">
                                <img src="images/f-icon.png" alt="Facebook" class="w-4 h-4">
                            </a> -->
                            <a href="https://www.linkedin.com/company/suyash-impex-pvt-ltd/" class="text-gray-400 hover:text-primary transition-colors duration-200">
                                <img src="images/in-icon.png" alt="LinkedIn" class="w-4 h-4">
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scroll to Top Button -->
    <button id="goTop" class="fixed bottom-8 right-8 w-12 h-12 bg-primary hover:bg-accent text-white rounded-full shadow-lg opacity-0 invisible transition-all duration-300 transform hover:scale-110 z-50">
        <svg class="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
        </svg>
    </button>

    <!-- JavaScript -->
    <script>
        // Mobile Menu Toggle
        class MobileMenu {
            constructor() {
                this.toggle = document.getElementById('mobileMenuToggle');
                this.menu = document.getElementById('mobileMenu');
                this.isOpen = false;
                this.init();
            }

            init() {
                this.toggle.addEventListener('click', () => this.toggleMenu());
                document.addEventListener('click', (e) => {
                    if (!this.toggle.contains(e.target) && !this.menu.contains(e.target)) {
                        this.closeMenu();
                    }
                });
            }

            toggleMenu() {
                this.isOpen = !this.isOpen;
                this.updateMenu();
            }

            closeMenu() {
                this.isOpen = false;
                this.updateMenu();
            }

            updateMenu() {
                const spans = this.toggle.querySelectorAll('span');
                if (this.isOpen) {
                    this.menu.style.transform = 'translateY(0)';
                    this.menu.style.opacity = '1';
                    spans[0].style.transform = 'rotate(45deg) translate(5px, 5px)';
                    spans[1].style.opacity = '0';
                    spans[2].style.transform = 'rotate(-45deg) translate(7px, -6px)';
                } else {
                    this.menu.style.transform = 'translateY(-100%)';
                    this.menu.style.opacity = '0';
                    spans.forEach(span => {
                        span.style.transform = 'none';
                        span.style.opacity = '1';
                    });
                }
            }
        }

        // Scroll Effects
        class ScrollEffects {
            constructor() {
                this.header = document.getElementById('header');
                this.goTopBtn = document.getElementById('goTop');
                this.init();
            }

            init() {
                window.addEventListener('scroll', () => this.handleScroll());
                this.goTopBtn.addEventListener('click', () => this.scrollToTop());
            }

            handleScroll() {
                const scrollTop = window.pageYOffset;

                // Go to top button
                if (scrollTop > 300) {
                    this.goTopBtn.classList.remove('opacity-0', 'invisible');
                    this.goTopBtn.classList.add('opacity-100', 'visible');
                } else {
                    this.goTopBtn.classList.add('opacity-0', 'invisible');
                    this.goTopBtn.classList.remove('opacity-100', 'visible');
                }
            }

            scrollToTop() {
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }
        }

        // Initialize everything when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            // Hide loader
            const loader = document.getElementById('loader');
            setTimeout(() => {
                loader.style.opacity = '0';
                setTimeout(() => loader.style.display = 'none', 500);
            }, 1000);

            // Initialize components
            new MobileMenu();
            new ScrollEffects();
        });
    </script>
</body>
</html>
