{"designSystem": {"theme": {"primaryColor": "#f26722", "secondaryColor": "#0a0a0a", "accentColor": "#ffffff", "textColor": "#333333", "backgroundColor": "#ffffff", "fontFamily": "'Poppins', sans-serif", "buttonStyle": {"borderRadius": "4px", "padding": "10px 20px", "fontWeight": "600", "textTransform": "uppercase", "primaryBackground": "#f26722", "hoverEffect": {"backgroundColor": "#e05310", "transition": "0.3s ease"}}}, "layout": {"maxWidth": "1200px", "gridSystem": "12-column", "padding": {"desktop": "60px 20px", "mobile": "40px 15px"}, "sectionSpacing": "80px", "responsiveBreakpoints": {"sm": "576px", "md": "768px", "lg": "992px", "xl": "1200px"}}, "components": {"header": {"type": "sticky", "layout": "horizontal", "elements": ["logo", "navLinks", "socialIcons", "contactInfo"], "background": "#ffffff", "shadow": "light"}, "hero": {"backgroundImage": true, "overlay": {"color": "rgba(0,0,0,0.4)", "textAlign": "left"}, "title": {"fontSize": "2.5rem", "fontWeight": "bold"}, "button": "primary"}, "services": {"layout": "cardGrid", "card": {"iconPosition": "top", "textAlign": "center", "background": "#1a1a1a", "textColor": "#ffffff", "hoverEffect": {"scale": "1.05"}}}, "about": {"layout": "twoColumn", "elements": ["textContent", "image"], "highlightBox": {"background": "#f26722", "textColor": "#ffffff", "fontWeight": "bold"}}, "projectGallery": {"layout": "threeColumnGrid", "imageHoverEffect": "zoom", "spacing": "20px"}, "features": {"layout": "iconList", "icon": "left", "textAlignment": "left"}, "teamSection": {"layout": "threeColumnGrid", "card": {"profilePicture": true, "name": "bold", "role": "muted"}}, "testimonials": {"layout": "carousel", "card": {"background": "#ffffff", "quoteStyle": "italic", "author": "bold"}}, "logosSection": {"layout": "horizontalScroll", "logoSize": "80px"}, "faq": {"layout": "accordion", "questionStyle": {"fontWeight": "bold", "borderBottom": "1px solid #ddd"}}, "footer": {"layout": "threeColumn", "backgroundColor": "#0a0a0a", "textColor": "#ffffff", "elements": ["contactInfo", "quickLinks", "socialLinks"]}}, "typography": {"heading": {"fontWeight": "700", "lineHeight": "1.2", "color": "#0a0a0a"}, "subheading": {"fontWeight": "600", "color": "#f26722"}, "paragraph": {"fontWeight": "400", "lineHeight": "1.6", "color": "#333333"}, "smallText": {"fontSize": "0.85rem", "color": "#888888"}}, "animations": {"scrollReveal": true, "hoverLift": true, "buttonHoverScale": true}}}