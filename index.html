<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SUYASH - Precision Manufacturing Excellence</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Heroicons -->
    <script src="https://unpkg.com/heroicons@2.0.18/24/outline/index.js" type="module"></script>

    <!-- Custom Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#f26722',
                        secondary: '#0a0a0a',
                        accent: '#e05310',
                        gray: {
                            50: '#f9fafb',
                            100: '#f3f4f6',
                            200: '#e5e7eb',
                            300: '#d1d5db',
                            400: '#9ca3af',
                            500: '#6b7280',
                            600: '#4b5563',
                            700: '#374151',
                            800: '#1f2937',
                            900: '#111827',
                        }
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                        'display': ['Poppins', 'system-ui', 'sans-serif'],
                    },
                    animation: {
                        'fade-in-up': 'fadeInUp 0.6s ease-out',
                        'fade-in-down': 'fadeInDown 0.6s ease-out',
                        'fade-in-left': 'fadeInLeft 0.6s ease-out',
                        'fade-in-right': 'fadeInRight 0.6s ease-out',
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'bounce-slow': 'bounce 2s infinite',
                    },
                    keyframes: {
                        fadeInUp: {
                            '0%': { opacity: '0', transform: 'translateY(30px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        },
                        fadeInDown: {
                            '0%': { opacity: '0', transform: 'translateY(-30px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        },
                        fadeInLeft: {
                            '0%': { opacity: '0', transform: 'translateX(-30px)' },
                            '100%': { opacity: '1', transform: 'translateX(0)' }
                        },
                        fadeInRight: {
                            '0%': { opacity: '0', transform: 'translateX(30px)' },
                            '100%': { opacity: '1', transform: 'translateX(0)' }
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="font-sans text-gray-900 bg-white overflow-x-hidden">
    <!-- Loading Screen -->
    <div id="loader" class="fixed inset-0 z-50 flex items-center justify-center bg-white transition-opacity duration-500">
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-4 border-primary border-t-transparent"></div>
            <p class="mt-4 text-gray-600 font-medium">Loading Excellence...</p>
        </div>
    </div>

    <!-- Navigation Header -->
    <header id="header" class="fixed top-0 left-0 right-0 z-40 bg-white/95 backdrop-blur-md border-b border-gray-100 transition-all duration-300">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-20">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <a href="index.html" class="flex items-center space-x-3">
                        <img src="images/logo.png" alt="SUYASH" class="h-12 w-auto transition-transform duration-300 hover:scale-105" />
                        
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <nav class="hidden lg:flex items-center space-x-8">
                    <a href="index.html" class="text-primary font-semibold border-b-2 border-primary pb-1">Home</a>
                    <div class="relative group">
                        <button class="flex items-center space-x-1 text-gray-700 hover:text-primary transition-colors duration-200 font-medium">
                            <span>About</span>
                            <svg class="w-4 h-4 transition-transform duration-200 group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div class="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-100 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                            <div class="py-2">
                                <a href="suyash/history.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors duration-200">History</a>
                                <a href="suyash/locations.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors duration-200">Locations</a>
                                <a href="suyash/csr-activities.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors duration-200">CSR Activities</a>
                            </div>
                        </div>
                    </div>
                    <a href="suyash/services.html" class="text-gray-700 hover:text-primary transition-colors duration-200 font-medium">Services</a>
                    <a href="suyash/quality.html" class="text-gray-700 hover:text-primary transition-colors duration-200 font-medium">Quality</a>
                    <a href="suyash/careers.html" class="text-gray-700 hover:text-primary transition-colors duration-200 font-medium">Careers</a>
                    <a href="suyash/events.html" class="text-gray-700 hover:text-primary transition-colors duration-200 font-medium">Events</a>
                    <a href="suyash/contact-us.html" class="bg-primary text-white px-6 py-2 rounded-full hover:bg-accent transition-colors duration-200 font-medium">Contact Us</a>
                </nav>

                <!-- Mobile Menu Button -->
                <button id="mobileMenuToggle" class="lg:hidden flex flex-col items-center justify-center w-8 h-8 space-y-1.5">
                    <span class="w-6 h-0.5 bg-gray-700 transition-all duration-300"></span>
                    <span class="w-6 h-0.5 bg-gray-700 transition-all duration-300"></span>
                    <span class="w-6 h-0.5 bg-gray-700 transition-all duration-300"></span>
                </button>
            </div>
        </div>

        <!-- Mobile Navigation Menu -->
        <div id="mobileMenu" class="lg:hidden fixed inset-x-0 top-20 bg-white border-b border-gray-100 shadow-lg transform -translate-y-full opacity-0 transition-all duration-300">
            <div class="px-4 py-6 space-y-4">
                <a href="index.html" class="block text-primary font-semibold">Home</a>
                <div class="space-y-2">
                    <p class="text-gray-900 font-medium">About</p>
                    <div class="pl-4 space-y-2">
                        <a href="suyash/history.html" class="block text-gray-600 hover:text-primary transition-colors duration-200">History</a>
                        <a href="suyash/locations.html" class="block text-gray-600 hover:text-primary transition-colors duration-200">Locations</a>
                        <a href="suyash/csr-activities.html" class="block text-gray-600 hover:text-primary transition-colors duration-200">CSR Activities</a>
                    </div>
                </div>
                <a href="suyash/services.html" class="block text-gray-700 hover:text-primary transition-colors duration-200">Services</a>
                <a href="suyash/quality.html" class="block text-gray-700 hover:text-primary transition-colors duration-200">Quality</a>
                <a href="suyash/careers.html" class="block text-gray-700 hover:text-primary transition-colors duration-200">Careers</a>
                <a href="suyash/events.html" class="block text-gray-700 hover:text-primary transition-colors duration-200">Events</a>
                <a href="suyash/contact-us.html" class="block bg-primary text-white px-6 py-3 rounded-full text-center hover:bg-accent transition-colors duration-200">Contact Us</a>
            </div>
        </div>
    </header>

    <!-- Hero Section with Slider -->
    <section id="heroSlider" class="relative h-screen overflow-hidden mt-20">
        <!-- Slider Container -->
        <div class="relative w-full h-full">
            <!-- Slide 1 -->
            <div class="slide absolute inset-0 opacity-100 transition-opacity duration-1000">
                <div class="absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-transparent z-10"></div>
                <img src="images/slide/home-banner-1.jpg" alt="Suyash Manufacturing" class="w-full h-full object-cover">
                <div class="absolute inset-0 z-20 flex items-center">
                    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div class="max-w-3xl">
                            <h1 class="text-5xl md:text-7xl font-bold text-white mb-6 animate-fade-in-up">
                                Precision <span class="text-primary">Manufacturing</span> Excellence
                            </h1>
                            <p class="text-xl md:text-2xl text-gray-200 mb-8 animate-fade-in-up" style="animation-delay: 0.2s">
                                Since 1958, crafting engineering solutions that power industry leaders worldwide
                            </p>
                            <div class="flex flex-col sm:flex-row gap-4 animate-fade-in-up" style="animation-delay: 0.4s">
                                <a href="suyash/services.html" class="bg-primary hover:bg-accent text-white px-8 py-4 rounded-full font-semibold text-lg transition-all duration-300 transform hover:scale-105 hover:shadow-lg text-center">
                                    Explore Our Services
                                </a>
                                <a href="suyash/contact-us.html" class="border-2 border-white text-white hover:bg-white hover:text-gray-900 px-8 py-4 rounded-full font-semibold text-lg transition-all duration-300 text-center">
                                    Get In Touch
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 2 -->
            <div class="slide absolute inset-0 opacity-0 transition-opacity duration-1000">
                <div class="absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-transparent z-10"></div>
                <img src="images/slide/home-banner-2-2.jpg" alt="Suyash Facility" class="w-full h-full object-cover">
                <div class="absolute inset-0 z-20 flex items-center">
                    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div class="max-w-3xl">
                            <h1 class="text-5xl md:text-7xl font-bold text-white mb-6">
                                State-of-the-Art <span class="text-primary">Facilities</span>
                            </h1>
                            <p class="text-xl md:text-2xl text-gray-200 mb-8">
                                Modern manufacturing capabilities across multiple locations
                            </p>
                            <div class="flex flex-col sm:flex-row gap-4">
                                <a href="suyash/locations.html" class="bg-primary hover:bg-accent text-white px-8 py-4 rounded-full font-semibold text-lg transition-all duration-300 transform hover:scale-105 hover:shadow-lg text-center">
                                    Our Locations
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional slides for other images -->
            <div class="slide absolute inset-0 opacity-0 transition-opacity duration-1000">
                <div class="absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-transparent z-10"></div>
                <img src="images/slide/1.jpg" alt="Manufacturing Process" class="w-full h-full object-cover">
                <div class="absolute inset-0 z-20 flex items-center">
                    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div class="max-w-3xl">
                            <h1 class="text-5xl md:text-7xl font-bold text-white mb-6">
                                Advanced <span class="text-primary">Manufacturing</span>
                            </h1>
                            <p class="text-xl md:text-2xl text-gray-200 mb-8">
                                Cutting-edge processes delivering exceptional quality
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="slide absolute inset-0 opacity-0 transition-opacity duration-1000">
                <div class="absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-transparent z-10"></div>
                <img src="images/slide/2.jpg" alt="Quality Control" class="w-full h-full object-cover">
                <div class="absolute inset-0 z-20 flex items-center">
                    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div class="max-w-3xl">
                            <h1 class="text-5xl md:text-7xl font-bold text-white mb-6">
                                Uncompromising <span class="text-primary">Quality</span>
                            </h1>
                            <p class="text-xl md:text-2xl text-gray-200 mb-8">
                                ISO certified processes ensuring excellence in every component
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="slide absolute inset-0 opacity-0 transition-opacity duration-1000">
                <div class="absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-transparent z-10"></div>
                <img src="images/slide/3.jpg" alt="Production Line" class="w-full h-full object-cover">
            </div>

            <div class="slide absolute inset-0 opacity-0 transition-opacity duration-1000">
                <div class="absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-transparent z-10"></div>
                <img src="images/slide/4.jpg" alt="Engineering Excellence" class="w-full h-full object-cover">
            </div>

            <div class="slide absolute inset-0 opacity-0 transition-opacity duration-1000">
                <div class="absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-transparent z-10"></div>
                <img src="images/slide/5.jpg" alt="Modern Equipment" class="w-full h-full object-cover">
            </div>

            <div class="slide absolute inset-0 opacity-0 transition-opacity duration-1000">
                <div class="absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-transparent z-10"></div>
                <img src="images/slide/6.jpg" alt="Precision Manufacturing" class="w-full h-full object-cover">
            </div>

            <div class="slide absolute inset-0 opacity-0 transition-opacity duration-1000">
                <div class="absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-transparent z-10"></div>
                <img src="images/slide/7.jpg" alt="Industrial Operations" class="w-full h-full object-cover">
            </div>

            <div class="slide absolute inset-0 opacity-0 transition-opacity duration-1000">
                <div class="absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-transparent z-10"></div>
                <img src="images/slide/8.jpg" alt="Manufacturing Excellence" class="w-full h-full object-cover">
            </div>
        </div>

        <!-- Navigation Arrows -->
        <button id="prevBtn" class="absolute left-6 top-1/2 transform -translate-y-1/2 z-30 bg-white/20 hover:bg-white/30 backdrop-blur-sm text-white p-3 rounded-full transition-all duration-300 hover:scale-110">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
        </button>
        <button id="nextBtn" class="absolute right-6 top-1/2 transform -translate-y-1/2 z-30 bg-white/20 hover:bg-white/30 backdrop-blur-sm text-white p-3 rounded-full transition-all duration-300 hover:scale-110">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
        </button>

        <!-- Slide Indicators -->
        <div id="sliderDots" class="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-30 flex space-x-3">
            <!-- Dots will be generated by JavaScript -->
        </div>

        <!-- Scroll Down Indicator -->
        <div class="absolute bottom-8 left-8 z-30 hidden lg:block">
            <div class="flex items-center space-x-3 text-white/80">
                <div class="w-px h-16 bg-white/40"></div>
                <p class="text-sm font-medium rotate-90 origin-left">Scroll Down</p>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid lg:grid-cols-2 gap-16 items-center">
                <!-- Video Section -->
                <div class="order-2 lg:order-1">
                    <div class="relative rounded-2xl overflow-hidden shadow-2xl bg-white p-2">
                        <div class="aspect-video rounded-xl overflow-hidden">
                            <iframe
                                width="100%"
                                height="100%"
                                src="https://www.youtube.com/embed/i9mSbINmUtA"
                                frameborder="0"
                                allowfullscreen
                                class="w-full h-full"
                            ></iframe>
                        </div>
                    </div>
                    <!-- Stats Cards -->
                    <div class="grid grid-cols-3 gap-4 mt-8">
                        <div class="bg-white rounded-xl p-6 shadow-lg text-center transform hover:scale-105 transition-transform duration-300">
                            <div class="text-3xl font-bold text-primary mb-2">65+</div>
                            <div class="text-sm text-gray-600">Years of Excellence</div>
                        </div>
                        <div class="bg-white rounded-xl p-6 shadow-lg text-center transform hover:scale-105 transition-transform duration-300">
                            <div class="text-3xl font-bold text-primary mb-2">500+</div>
                            <div class="text-sm text-gray-600">Global Clients</div>
                        </div>
                        <div class="bg-white rounded-xl p-6 shadow-lg text-center transform hover:scale-105 transition-transform duration-300">
                            <div class="text-3xl font-bold text-primary mb-2">ISO</div>
                            <div class="text-sm text-gray-600">Certified Quality</div>
                        </div>
                    </div>
                </div>

                <!-- Content Section -->
                <div class="order-1 lg:order-2">
                    <div class="mb-8">
                        <span class="inline-block px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-semibold mb-4">
                            Our Story
                        </span>
                        <h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                            About <span class="text-primary">Suyash</span>
                        </h2>
                    </div>

                    <div class="prose prose-lg text-gray-600 leading-relaxed">
                        <p class="mb-6">
                            In 1958, a young skilled toolmaker, Y. N. Chaphekar decided to start his own business, a business of something he was extremely good at – tool design & manufacturing. What started as a small workshop in Parel grew into a multi-location, multi-million rupee manufacturing facility supplying a variety of engineering components to some of the giants of the Indian industry.
                        </p>

                        <p class="mb-6">
                            Chaphekars became a name to reckon with in the automotive sector. The supplies varied from a small precision engineered pin to large load bodies for trucks – Chaphekars were supplying them all. They also became reputed as dependable suppliers of wiper motors & windscreen washers.
                        </p>

                        <p class="mb-8">
                            Equipped with the knowledge & experience gained through years of servicing one of the most demanding industry, Chaphekars decided to widen the horizons of business. They decided to go beyond the boundaries of India. The youngest son of the Founder, Subhash Chaphekar founded Suyash Impex to deal with exports of components & assemblies.
                        </p>
                    </div>

                    <!-- Key Features -->
                    <div class="grid grid-cols-2 gap-4 mt-8">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <span class="text-gray-700 font-medium">Global Reach</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <span class="text-gray-700 font-medium">Precision Engineering</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <span class="text-gray-700 font-medium">Quality Assurance</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <span class="text-gray-700 font-medium">Innovation Focus</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Section Header -->
            <div class="text-center mb-16">
                <span class="inline-block px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-semibold mb-4">
                    What We Do
                </span>
                <h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                    Our <span class="text-primary">Services</span>
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Comprehensive manufacturing solutions backed by decades of expertise and cutting-edge technology
                </p>
            </div>

            <!-- Services Grid -->
            <div class="grid md:grid-cols-3 gap-8 mb-12">
                <!-- Metal Forming Service -->
                <div class="group bg-gradient-to-br from-gray-900 to-gray-800 rounded-2xl p-8 text-white transform hover:scale-105 transition-all duration-300 hover:shadow-2xl">
                    <div class="mb-6">
                        <div class="w-16 h-16 bg-primary/20 rounded-xl flex items-center justify-center mb-4 group-hover:bg-primary/30 transition-colors duration-300">
                            <img src="images/1_Metal-Forming.png" alt="Metal Forming" class="w-10 h-10 object-contain">
                        </div>
                        <h3 class="text-2xl font-bold mb-4 group-hover:text-primary transition-colors duration-300">Metal Forming</h3>
                        <p class="text-gray-300 leading-relaxed mb-6">
                            Over 3 decades of manufacturing a variety of sheet metal components and fabrication...
                        </p>
                    </div>

                    <!-- Service Features -->
                    <div class="space-y-3 mb-6">
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 bg-primary rounded-full"></div>
                            <span class="text-sm text-gray-300">Sheet Metal Components</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 bg-primary rounded-full"></div>
                            <span class="text-sm text-gray-300">Custom Fabrication</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 bg-primary rounded-full"></div>
                            <span class="text-sm text-gray-300">Precision Forming</span>
                        </div>
                    </div>

                    <div class="pt-4 border-t border-gray-700">
                        <a href="suyash/services.html#metalforming" class="inline-flex items-center text-primary hover:text-white transition-colors duration-300 font-medium">
                            Learn More
                            <svg class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Metal Cutting Service -->
                <div class="group bg-gradient-to-br from-gray-900 to-gray-800 rounded-2xl p-8 text-white transform hover:scale-105 transition-all duration-300 hover:shadow-2xl">
                    <div class="mb-6">
                        <div class="w-16 h-16 bg-primary/20 rounded-xl flex items-center justify-center mb-4 group-hover:bg-primary/30 transition-colors duration-300">
                            <img src="images/2_Metal-Cutting.png" alt="Metal Cutting" class="w-10 h-10 object-contain">
                        </div>
                        <h3 class="text-2xl font-bold mb-4 group-hover:text-primary transition-colors duration-300">Metal Cutting</h3>
                        <p class="text-gray-300 leading-relaxed mb-6">
                            Armed with the experience of producing precision components by using a variety of metal...
                        </p>
                    </div>

                    <!-- Service Features -->
                    <div class="space-y-3 mb-6">
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 bg-primary rounded-full"></div>
                            <span class="text-sm text-gray-300">CNC Machining</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 bg-primary rounded-full"></div>
                            <span class="text-sm text-gray-300">Precision Cutting</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 bg-primary rounded-full"></div>
                            <span class="text-sm text-gray-300">Swiss Lathe Operations</span>
                        </div>
                    </div>

                    <div class="pt-4 border-t border-gray-700">
                        <a href="suyash/services.html#metalcutting" class="inline-flex items-center text-primary hover:text-white transition-colors duration-300 font-medium">
                            Learn More
                            <svg class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Assemblies Service -->
                <div class="group bg-gradient-to-br from-gray-900 to-gray-800 rounded-2xl p-8 text-white transform hover:scale-105 transition-all duration-300 hover:shadow-2xl">
                    <div class="mb-6">
                        <div class="w-16 h-16 bg-primary/20 rounded-xl flex items-center justify-center mb-4 group-hover:bg-primary/30 transition-colors duration-300">
                            <img src="images/3_Assemblies.png" alt="Assemblies" class="w-10 h-10 object-contain">
                        </div>
                        <h3 class="text-2xl font-bold mb-4 group-hover:text-primary transition-colors duration-300">Assemblies</h3>
                        <p class="text-gray-300 leading-relaxed mb-6">
                            Our journey upwards in the value chain began long ago with welded assemblies supplied...
                        </p>
                    </div>

                    <!-- Service Features -->
                    <div class="space-y-3 mb-6">
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 bg-primary rounded-full"></div>
                            <span class="text-sm text-gray-300">Welded Assemblies</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 bg-primary rounded-full"></div>
                            <span class="text-sm text-gray-300">Complex Integration</span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 bg-primary rounded-full"></div>
                            <span class="text-sm text-gray-300">Quality Testing</span>
                        </div>
                    </div>

                    <div class="pt-4 border-t border-gray-700">
                        <a href="suyash/services.html#assembly" class="inline-flex items-center text-primary hover:text-white transition-colors duration-300 font-medium">
                            Learn More
                            <svg class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Call to Action -->
            <div class="text-center">
                <a href="suyash/services.html" class="inline-flex items-center bg-primary hover:bg-accent text-white px-8 py-4 rounded-full font-semibold text-lg transition-all duration-300 transform hover:scale-105 hover:shadow-lg">
                    Explore All Services
                    <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </a>
            </div>
        </div>
    </section>

    <!-- Leadership Section -->
    <section class="py-20 bg-gradient-to-br from-gray-50 to-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid lg:grid-cols-2 gap-16 items-center">
                <!-- Image Section -->
                <div class="order-2 lg:order-1">
                    <div class="relative">
                        <div class="absolute inset-0 bg-gradient-to-r from-primary/20 to-accent/20 rounded-2xl transform rotate-3"></div>
                        <div class="relative bg-white rounded-2xl p-4 shadow-2xl">
                            <img src="images/leader.jpg" alt="Leadership" class="w-full h-auto rounded-xl object-cover">
                        </div>
                        <!-- Decorative Elements -->
                        <div class="absolute -top-4 -right-4 w-24 h-24 bg-primary/10 rounded-full"></div>
                        <div class="absolute -bottom-6 -left-6 w-32 h-32 bg-accent/10 rounded-full"></div>
                    </div>
                </div>

                <!-- Content Section -->
                <div class="order-1 lg:order-2">
                    <div class="mb-8">
                        <span class="inline-block px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-semibold mb-4">
                            Our Leaders
                        </span>
                        <h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                            Visionary <span class="text-primary">Leadership</span>
                        </h2>
                    </div>

                    <!-- Leadership Profiles -->
                    <div class="space-y-8">
                        <!-- Chairman & Managing Director -->
                        <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow duration-300">
                            <div class="flex items-start space-x-4">
                                <div class="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0">
                                    <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-xl font-bold text-gray-900 mb-2">
                                        Subhash Y. Chaphekar – Chairman & Managing Director
                                    </h3>
                                    <p class="text-gray-600 leading-relaxed">
                                        A qualified engineer trained under the tutelage of the Founder. He has engineering in his blood & is immensely passionate about manufacturing as a subject. He loves solving manufacturing challanges & is the primary force behind manufacturing innovations at Suyash.
                                    </p>
                                    <div class="flex items-center space-x-4 mt-4">
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary">
                                            Engineering Excellence
                                        </span>
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            Innovation Leader
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- CEO -->
                        <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow duration-300">
                            <div class="flex items-start space-x-4">
                                <div class="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0">
                                    <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-xl font-bold text-gray-900 mb-2">
                                        Amol S. Chaphekar – CEO
                                    </h3>
                                    <p class="text-gray-600 leading-relaxed">
                                        The young & dynamic CEO of Suyash is an engineer & a B-School graduate. Amol is a street smart leader with experience in a variety of business disciplines like operations, purchase, marketing & sales. Apart from building a profitable organization, Amol works on building an efficient enterprise by working with the production team on productivity enhancement
                                    </p>
                                    <div class="flex items-center space-x-4 mt-4">
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary">
                                            Strategic Vision
                                        </span>
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            Business Excellence
                                        </span>
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                            Operations Expert
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Leadership Values -->
                    <div class="mt-8 grid grid-cols-2 gap-4">
                        <div class="text-center p-4 bg-white rounded-lg shadow-md">
                            <div class="text-2xl font-bold text-primary mb-1">Innovation</div>
                            <div class="text-sm text-gray-600">Driven Excellence</div>
                        </div>
                        <div class="text-center p-4 bg-white rounded-lg shadow-md">
                            <div class="text-2xl font-bold text-primary mb-1">Quality</div>
                            <div class="text-sm text-gray-600">First Approach</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Milestones Timeline Section -->
    <section class="py-20 bg-white relative overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-5">
            <div class="absolute inset-0" style="background-image: radial-gradient(circle at 1px 1px, #f26722 1px, transparent 0); background-size: 20px 20px;"></div>
        </div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
            <!-- Section Header -->
            <div class="text-center mb-16">
                <span class="inline-block px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-semibold mb-4">
                    Our Journey
                </span>
                <h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
                    Historic <span class="text-primary">Milestones</span>
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Six decades of innovation, growth, and excellence in manufacturing
                </p>
            </div>

            <!-- Timeline Container -->
            <div class="relative">
                <!-- Central Timeline Line -->
                <div class="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-primary via-accent to-primary rounded-full hidden lg:block"></div>

                <!-- Timeline Items -->
                <div class="space-y-12 lg:space-y-16">
                    <!-- 1958 -->
                    <div class="relative flex items-center lg:justify-between">
                        <div class="lg:w-5/12 lg:text-right lg:pr-8">
                            <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                                <div class="flex items-center justify-between mb-4 lg:flex-row-reverse">
                                    <div class="w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center text-white font-bold text-xl shadow-lg">
                                        1958
                                    </div>
                                    <div class="hidden lg:block w-8 h-px bg-primary"></div>
                                </div>
                                <blockquote class="text-gray-700 leading-relaxed italic">
                                    "Late Mr. Y. N. Chaphekar laid the foundation of entrepreneurship by starting a tool room with a modest capital of Rs. 5,000 (USD 110)."
                                </blockquote>
                                <div class="mt-4 flex items-center lg:justify-end">
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary">
                                        Foundation
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-primary rounded-full border-4 border-white shadow-lg hidden lg:block"></div>
                        <div class="lg:w-5/12"></div>
                    </div>

                    <!-- 1975 -->
                    <div class="relative flex items-center lg:justify-between">
                        <div class="lg:w-5/12"></div>
                        <div class="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-primary rounded-full border-4 border-white shadow-lg hidden lg:block"></div>
                        <div class="lg:w-5/12 lg:pl-8">
                            <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center text-white font-bold text-xl shadow-lg">
                                        1975
                                    </div>
                                    <div class="hidden lg:block w-8 h-px bg-primary"></div>
                                </div>
                                <blockquote class="text-gray-700 leading-relaxed italic">
                                    "Mr. Subhash Chaphekar joined his father & his three elder brothers in business and soon it received wide appreciation from reputed companies like ABB, Siemens & Crompton for supplies of tools."
                                </blockquote>
                                <div class="mt-4">
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        Growth
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 1980 -->
                    <div class="relative flex items-center lg:justify-between">
                        <div class="lg:w-5/12 lg:text-right lg:pr-8">
                            <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                                <div class="flex items-center justify-between mb-4 lg:flex-row-reverse">
                                    <div class="w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center text-white font-bold text-xl shadow-lg">
                                        1980
                                    </div>
                                    <div class="hidden lg:block w-8 h-px bg-primary"></div>
                                </div>
                                <blockquote class="text-gray-700 leading-relaxed italic">
                                    "Within a short span of time, the business amassed reputed customers such as IBM, Crompton Greaves, Larsen & Toubro, Tata Motors, Bajaj Automotive, Mahindra & Mahindra, etc."
                                </blockquote>
                                <div class="mt-4 flex items-center lg:justify-end">
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Expansion
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-primary rounded-full border-4 border-white shadow-lg hidden lg:block"></div>
                        <div class="lg:w-5/12"></div>
                    </div>

                    <!-- 1982 -->
                    <div class="relative flex items-center lg:justify-between">
                        <div class="lg:w-5/12"></div>
                        <div class="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-primary rounded-full border-4 border-white shadow-lg hidden lg:block"></div>
                        <div class="lg:w-5/12 lg:pl-8">
                            <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center text-white font-bold text-xl shadow-lg">
                                        1982
                                    </div>
                                    <div class="hidden lg:block w-8 h-px bg-primary"></div>
                                </div>
                                <blockquote class="text-gray-700 leading-relaxed italic">
                                    "Tata Motors encouraged them to setup a manufacturing plant in Pune for producing cargo bodies for their Light Commercial Vehicles."
                                </blockquote>
                                <div class="mt-4">
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                        Manufacturing
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 1999 -->
                    <div class="relative flex items-center lg:justify-between">
                        <div class="lg:w-5/12 lg:text-right lg:pr-8">
                            <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                                <div class="flex items-center justify-between mb-4 lg:flex-row-reverse">
                                    <div class="w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center text-white font-bold text-xl shadow-lg">
                                        1999
                                    </div>
                                    <div class="hidden lg:block w-8 h-px bg-primary"></div>
                                </div>
                                <blockquote class="text-gray-700 leading-relaxed italic">
                                    "Chaphekar Group was restructured and Subhash Chaphekar chose to carve his own niche and set up Suyash Impex Pvt. Ltd."
                                </blockquote>
                                <div class="mt-4 flex items-center lg:justify-end">
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary">
                                        New Beginning
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-primary rounded-full border-4 border-white shadow-lg hidden lg:block"></div>
                        <div class="lg:w-5/12"></div>
                    </div>

                    <!-- 2001 -->
                    <div class="relative flex items-center lg:justify-between">
                        <div class="lg:w-5/12"></div>
                        <div class="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-primary rounded-full border-4 border-white shadow-lg hidden lg:block"></div>
                        <div class="lg:w-5/12 lg:pl-8">
                            <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center text-white font-bold text-xl shadow-lg">
                                        2001
                                    </div>
                                    <div class="hidden lg:block w-8 h-px bg-primary"></div>
                                </div>
                                <blockquote class="text-gray-700 leading-relaxed italic">
                                    "Suyash bagged first export order of Fencing Accessories to Sweden"
                                </blockquote>
                                <div class="mt-4">
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        Global Reach
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 2003 -->
                    <div class="relative flex items-center lg:justify-between">
                        <div class="lg:w-5/12 lg:text-right lg:pr-8">
                            <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                                <div class="flex items-center justify-between mb-4 lg:flex-row-reverse">
                                    <div class="w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center text-white font-bold text-xl shadow-lg">
                                        2003
                                    </div>
                                    <div class="hidden lg:block w-8 h-px bg-primary"></div>
                                </div>
                                <blockquote class="text-gray-700 leading-relaxed italic">
                                    "Started exports of Fence Posts to Norway"
                                </blockquote>
                                <div class="mt-4 flex items-center lg:justify-end">
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Export Growth
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-primary rounded-full border-4 border-white shadow-lg hidden lg:block"></div>
                        <div class="lg:w-5/12"></div>
                    </div>

                    <!-- 2005 -->
                    <div class="relative flex items-center lg:justify-between">
                        <div class="lg:w-5/12"></div>
                        <div class="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-primary rounded-full border-4 border-white shadow-lg hidden lg:block"></div>
                        <div class="lg:w-5/12 lg:pl-8">
                            <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center text-white font-bold text-xl shadow-lg">
                                        2005
                                    </div>
                                    <div class="hidden lg:block w-8 h-px bg-primary"></div>
                                </div>
                                <blockquote class="text-gray-700 leading-relaxed italic">
                                    "Started exports of Fuel storage tank fittings to UK"
                                </blockquote>
                                <div class="mt-4">
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        UK Market
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 2008 -->
                    <div class="relative flex items-center lg:justify-between">
                        <div class="lg:w-5/12 lg:text-right lg:pr-8">
                            <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                                <div class="flex items-center justify-between mb-4 lg:flex-row-reverse">
                                    <div class="w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center text-white font-bold text-xl shadow-lg">
                                        2008
                                    </div>
                                    <div class="hidden lg:block w-8 h-px bg-primary"></div>
                                </div>
                                <blockquote class="text-gray-700 leading-relaxed italic">
                                    "Dispatched first Fuel Storage tank to USA.<br />
                                    Imported first CNC swiss lathe from Tsugami, Japan."
                                </blockquote>
                                <div class="mt-4 flex items-center lg:justify-end space-x-2">
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        USA Market
                                    </span>
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                                        Technology
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-primary rounded-full border-4 border-white shadow-lg hidden lg:block"></div>
                        <div class="lg:w-5/12"></div>
                    </div>

                    <!-- 2010 -->
                    <div class="relative flex items-center lg:justify-between">
                        <div class="lg:w-5/12"></div>
                        <div class="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-primary rounded-full border-4 border-white shadow-lg hidden lg:block"></div>
                        <div class="lg:w-5/12 lg:pl-8">
                            <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center text-white font-bold text-xl shadow-lg">
                                        2010
                                    </div>
                                    <div class="hidden lg:block w-8 h-px bg-primary"></div>
                                </div>
                                <blockquote class="text-gray-700 leading-relaxed italic">
                                    "Received Silver Supplier Award from Larsen & Toubro, India's biggest switch gear and infrastructure conglomerate for supply of Electromechanical Assemblies."
                                </blockquote>
                                <div class="mt-4">
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        🏆 Award
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 2011 -->
                    <div class="relative flex items-center lg:justify-between">
                        <div class="lg:w-5/12 lg:text-right lg:pr-8">
                            <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                                <div class="flex items-center justify-between mb-4 lg:flex-row-reverse">
                                    <div class="w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center text-white font-bold text-xl shadow-lg">
                                        2011
                                    </div>
                                    <div class="hidden lg:block w-8 h-px bg-primary"></div>
                                </div>
                                <blockquote class="text-gray-700 leading-relaxed italic">
                                    "Started exports of precision turned components to Spain<br />
                                    Started bulk exports of Above Ground Fuel Storage tanks from a dedicated facility in Chakan, Pune, in collaboration with group company, Chaphekar Suspensions Pvt. Ltd."
                                </blockquote>
                                <div class="mt-4 flex items-center lg:justify-end space-x-2">
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                        Spain Market
                                    </span>
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-teal-100 text-teal-800">
                                        Facility Expansion
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-primary rounded-full border-4 border-white shadow-lg hidden lg:block"></div>
                        <div class="lg:w-5/12"></div>
                    </div>

                    <!-- 2012 -->
                    <div class="relative flex items-center lg:justify-between">
                        <div class="lg:w-5/12"></div>
                        <div class="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-primary rounded-full border-4 border-white shadow-lg hidden lg:block"></div>
                        <div class="lg:w-5/12 lg:pl-8">
                            <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center text-white font-bold text-xl shadow-lg">
                                        2012
                                    </div>
                                    <div class="hidden lg:block w-8 h-px bg-primary"></div>
                                </div>
                                <blockquote class="text-gray-700 leading-relaxed italic">
                                    "Suyash received 'Export House Status' recognition from Government of India's Ministry of Commerce & Industry"
                                </blockquote>
                                <div class="mt-4">
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        🏛️ Government Recognition
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 2013 -->
                    <div class="relative flex items-center lg:justify-between">
                        <div class="lg:w-5/12 lg:text-right lg:pr-8">
                            <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                                <div class="flex items-center justify-between mb-4 lg:flex-row-reverse">
                                    <div class="w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center text-white font-bold text-xl shadow-lg">
                                        2013
                                    </div>
                                    <div class="hidden lg:block w-8 h-px bg-primary"></div>
                                </div>
                                <blockquote class="text-gray-700 leading-relaxed italic">
                                    "Setup brand new Headquarters in Navi Mumbai with a state-of-the-art office and factory."
                                </blockquote>
                                <div class="mt-4 flex items-center lg:justify-end">
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        🏢 New HQ
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-primary rounded-full border-4 border-white shadow-lg hidden lg:block"></div>
                        <div class="lg:w-5/12"></div>
                    </div>

                    <!-- 2014 -->
                    <div class="relative flex items-center lg:justify-between">
                        <div class="lg:w-5/12"></div>
                        <div class="absolute left-1/2 transform -translate-x-1/2 w-6 h-6 bg-gradient-to-br from-primary to-accent rounded-full border-4 border-white shadow-lg hidden lg:block"></div>
                        <div class="lg:w-5/12 lg:pl-8">
                            <div class="bg-gradient-to-br from-primary to-accent rounded-2xl p-6 text-white shadow-2xl transform hover:-translate-y-1 transition-all duration-300">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center text-white font-bold text-xl shadow-lg">
                                        2014
                                    </div>
                                    <div class="hidden lg:block w-8 h-px bg-white/40"></div>
                                </div>
                                <blockquote class="text-white leading-relaxed italic">
                                    "Received ISO 9001:2008 certification from URS / UKAS, United Kingdom"
                                </blockquote>
                                <div class="mt-4">
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-white/20 text-white">
                                        🎖️ ISO Certified
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gradient-to-br from-gray-900 to-black text-white">
        <!-- Main Footer Content -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <div class="grid lg:grid-cols-4 md:grid-cols-2 gap-8">
                <!-- Company Info -->
                <div class="lg:col-span-2">
                    <div class="flex items-center space-x-3 mb-6">
                        <img src="images/logo.png" alt="SUYASH" class="h-12 w-auto" />
                        <div>
                            <h3 class="text-2xl font-bold">SUYASH</h3>
                            <p class="text-gray-400 text-sm">Manufacturing Excellence Since 1958</p>
                        </div>
                    </div>
                    <p class="text-gray-300 leading-relaxed mb-6 max-w-md">
                        Leading manufacturer of precision engineering components, serving global markets with innovative solutions and uncompromising quality.
                    </p>
                    <div class="flex space-x-4">
                        <!-- <a href="#" class="w-10 h-10 bg-primary/20 hover:bg-primary rounded-full flex items-center justify-center transition-colors duration-300">
                            <img src="images/f-icon.png" alt="Facebook" class="w-5 h-5">
                        </a> -->
                        <a href="#" class="w-10 h-10 bg-primary/20 hover:bg-primary rounded-full flex items-center justify-center transition-colors duration-300">
                            <img src="images/in-icon.png" alt="LinkedIn" class="w-5 h-5">
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="text-lg font-semibold mb-6">Quick Links</h4>
                    <ul class="space-y-3">
                        <li><a href="index.html" class="text-gray-300 hover:text-primary transition-colors duration-200">Home</a></li>
                        <li><a href="suyash/history.html" class="text-gray-300 hover:text-primary transition-colors duration-200">History</a></li>
                        <li><a href="suyash/services.html" class="text-gray-300 hover:text-primary transition-colors duration-200">Services</a></li>
                        <li><a href="suyash/quality.html" class="text-gray-300 hover:text-primary transition-colors duration-200">Quality</a></li>
                        <li><a href="suyash/careers.html" class="text-gray-300 hover:text-primary transition-colors duration-200">Careers</a></li>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div>
                    <h4 class="text-lg font-semibold mb-6">Get In Touch</h4>
                    <ul class="space-y-3">
                        <li><a href="suyash/events.html" class="text-gray-300 hover:text-primary transition-colors duration-200">Events</a></li>
                        <li><a href="suyash/contact-us.html" class="text-gray-300 hover:text-primary transition-colors duration-200">Contact Us</a></li>
                        <li><a href="suyash/locations.html" class="text-gray-300 hover:text-primary transition-colors duration-200">Our Locations</a></li>
                        <li><a href="suyash/csr-activities.html" class="text-gray-300 hover:text-primary transition-colors duration-200">CSR Activities</a></li>
                    </ul>

                    <!-- CTA Button -->
                    <div class="mt-6">
                        <a href="suyash/contact-us.html" class="inline-flex items-center bg-primary hover:bg-accent text-white px-6 py-3 rounded-full font-semibold transition-all duration-300 transform hover:scale-105">
                            Start Your Project
                            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer Bottom -->
        <div class="border-t border-gray-800">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-gray-400 text-sm">
                        All Right Reserved Suyash 2015 | Designed by : <a href="https://www.linkedin.com/in/shyam-varma/">SV</a>
                    </p>
                    <div class="flex items-center space-x-6 mt-4 md:mt-0">
                        <span class="text-gray-400 text-sm">Follow us</span>
                        <div class="flex space-x-3">
                            <!-- <a href="#" class="text-gray-400 hover:text-primary transition-colors duration-200">
                                <img src="images/f-icon.png" alt="Facebook" class="w-4 h-4">
                            </a> -->
                            <a href="https://www.linkedin.com/company/suyash-impex-pvt-ltd-/" class="text-gray-400 hover:text-primary transition-colors duration-200">
                                <img src="images/in-icon.png" alt="LinkedIn" class="w-4 h-4">
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scroll to Top Button -->
    <button id="goTop" class="fixed bottom-8 right-8 w-12 h-12 bg-primary hover:bg-accent text-white rounded-full shadow-lg opacity-0 invisible transition-all duration-300 transform hover:scale-110 z-50">
        <svg class="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
        </svg>
    </button>

    <!-- Modern JavaScript -->
    <script>
        // Modern Slider Implementation
        class HeroSlider {
            constructor() {
                this.slides = document.querySelectorAll('.slide');
                this.currentSlide = 0;
                this.totalSlides = this.slides.length;
                this.autoPlayInterval = null;
                this.init();
            }

            init() {
                this.createDots();
                this.bindEvents();
                this.startAutoPlay();
            }

            createDots() {
                const dotsContainer = document.getElementById('sliderDots');
                for (let i = 0; i < this.totalSlides; i++) {
                    const dot = document.createElement('button');
                    dot.className = `w-3 h-3 rounded-full transition-all duration-300 ${i === 0 ? 'bg-primary' : 'bg-white/50'}`;
                    dot.addEventListener('click', () => this.goToSlide(i));
                    dotsContainer.appendChild(dot);
                }
            }

            bindEvents() {
                document.getElementById('prevBtn').addEventListener('click', () => this.prevSlide());
                document.getElementById('nextBtn').addEventListener('click', () => this.nextSlide());

                // Keyboard navigation
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'ArrowLeft') this.prevSlide();
                    if (e.key === 'ArrowRight') this.nextSlide();
                });
            }

            showSlide(index) {
                this.slides.forEach((slide, i) => {
                    slide.style.opacity = i === index ? '1' : '0';
                });

                const dots = document.querySelectorAll('#sliderDots button');
                dots.forEach((dot, i) => {
                    dot.className = `w-3 h-3 rounded-full transition-all duration-300 ${i === index ? 'bg-primary' : 'bg-white/50'}`;
                });
            }

            nextSlide() {
                this.currentSlide = (this.currentSlide + 1) % this.totalSlides;
                this.showSlide(this.currentSlide);
            }

            prevSlide() {
                this.currentSlide = (this.currentSlide - 1 + this.totalSlides) % this.totalSlides;
                this.showSlide(this.currentSlide);
            }

            goToSlide(index) {
                this.currentSlide = index;
                this.showSlide(this.currentSlide);
            }

            startAutoPlay() {
                this.autoPlayInterval = setInterval(() => this.nextSlide(), 5000);
            }

            stopAutoPlay() {
                clearInterval(this.autoPlayInterval);
            }
        }

        // Mobile Menu Toggle
        class MobileMenu {
            constructor() {
                this.toggle = document.getElementById('mobileMenuToggle');
                this.menu = document.getElementById('mobileMenu');
                this.isOpen = false;
                this.init();
            }

            init() {
                this.toggle.addEventListener('click', () => this.toggleMenu());
                document.addEventListener('click', (e) => {
                    if (!this.toggle.contains(e.target) && !this.menu.contains(e.target)) {
                        this.closeMenu();
                    }
                });
            }

            toggleMenu() {
                this.isOpen = !this.isOpen;
                this.updateMenu();
            }

            closeMenu() {
                this.isOpen = false;
                this.updateMenu();
            }

            updateMenu() {
                const spans = this.toggle.querySelectorAll('span');
                if (this.isOpen) {
                    this.menu.style.transform = 'translateY(0)';
                    this.menu.style.opacity = '1';
                    spans[0].style.transform = 'rotate(45deg) translate(5px, 5px)';
                    spans[1].style.opacity = '0';
                    spans[2].style.transform = 'rotate(-45deg) translate(7px, -6px)';
                } else {
                    this.menu.style.transform = 'translateY(-100%)';
                    this.menu.style.opacity = '0';
                    spans.forEach(span => {
                        span.style.transform = 'none';
                        span.style.opacity = '1';
                    });
                }
            }
        }

        // Scroll Effects
        class ScrollEffects {
            constructor() {
                this.header = document.getElementById('header');
                this.goTopBtn = document.getElementById('goTop');
                this.init();
            }

            init() {
                window.addEventListener('scroll', () => this.handleScroll());
                this.goTopBtn.addEventListener('click', () => this.scrollToTop());
            }

            handleScroll() {
                const scrollTop = window.pageYOffset;

                // Header background
                if (scrollTop > 100) {
                    this.header.classList.add('bg-white/95', 'backdrop-blur-md');
                } else {
                    this.header.classList.remove('bg-white/95', 'backdrop-blur-md');
                }

                // Go to top button
                if (scrollTop > 300) {
                    this.goTopBtn.classList.remove('opacity-0', 'invisible');
                    this.goTopBtn.classList.add('opacity-100', 'visible');
                } else {
                    this.goTopBtn.classList.add('opacity-0', 'invisible');
                    this.goTopBtn.classList.remove('opacity-100', 'visible');
                }
            }

            scrollToTop() {
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }
        }

        // Initialize everything when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            // Hide loader
            const loader = document.getElementById('loader');
            setTimeout(() => {
                loader.style.opacity = '0';
                setTimeout(() => loader.style.display = 'none', 500);
            }, 1000);

            // Initialize components
            new HeroSlider();
            new MobileMenu();
            new ScrollEffects();

            // Smooth scroll for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({ behavior: 'smooth' });
                    }
                });
            });
        });
    </script>
</body>
</html>
