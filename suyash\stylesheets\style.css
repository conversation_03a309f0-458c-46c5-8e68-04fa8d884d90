/**
  * Name: Colores
  * Version: 1.0
  * Author: ROLLTHEMES
  * Author URI: http://www.rollthemes.com
*/

@import url("fonts.css");
@import url("font-awesome.css");
@import url("flexslider.css");
@import url("owl.carousel.css");
@import url("shortcodes.css");

/**  
  * Repeatable Patterns
  * Bootstrap Resetting Elements
  * Top
  * Header
  * Navigation
  * Mobile Navigation
  * Parallax
  * Page Title
  * Go Top Button
  * Switcher
  * Media Queries
*/

/* Repeatable Patterns
-------------------------------------------------------------- */
*,
*:before,
*:after {
    -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
            box-sizing: border-box;
}


.table-content{border-right:1px solid #999; border-top:1px solid #999; margin: 0 0 15px 0}

.table-content td{ padding:8px; border-bottom:1px solid #999; border-left:1px solid #999}

body {
  font: 14px/20px "Open Sans", sans-serif;
  background: #f4f4f4;
  color: #011d27;
  font-weight: 400;
}

.address-table{}
.address-table td{ padding: 8px 0;}


::-moz-selection { 
	color: #fff; 
	text-shadow: none; 
	background: #000; 
}

::-webkit-selection { 
	color: #fff; 
	text-shadow: none; 
	background: #000; 
}

::selection { 
	color: #fff; 
	text-shadow: none; 
	background: #000; 
}

a,
a:focus {
  color: #f7941e;
  outline: 0;
	text-decoration: none;
	-webkit-transition: all 0.3s ease-in-out;
	   -moz-transition: all 0.3s ease-in-out;
		 -ms-transition: all 0.3s ease-in-out;
		  -o-transition: all 0.3s ease-in-out;
			  transition: all 0.3s ease-in-out;
}

a:hover {
	color: #0f1521;
	text-decoration: none;
	-webkit-transition: all 0.3s ease-in-out;
	   -moz-transition: all 0.3s ease-in-out;
		 -ms-transition: all 0.3s ease-in-out;
		  -o-transition: all 0.3s ease-in-out;
			  transition: all 0.3s ease-in-out;
}

img {
	max-width: 100%;
    height: auto;
}

img.img-left {
	margin: 7px 30px 25px 0 !important;
	float: left;
}

img.img-right {
	margin: 7px 0 25px 30px !important;
	float: right;
}

strong, b {
	font-weight: 700;
}

p {
  margin-bottom: 20px; font: 14px/20px "Open Sans", sans-serif; text-align:justify !important;
}
.txt-alignC P{ text-align:center !important }

.margint1 { margin-top: 10px; }
.margint2 { margin-top: 20px; }
.margint3 { margin-top: 30px; }
.margint4 { margin-top: 40px; }
.margint5 { margin-top: 50px; }
.margint6 { margin-top: 60px; }
.marginb1 { margin-bottom: 10px; }
.marginb2 { margin-bottom: 20px; }
.marginb3 { margin-bottom: 30px; }
.marginb4 { margin-bottom: 40px; }
.marginb5 { margin-bottom: 50px; }
.marginb6 { margin-bottom: 60px; }

h1, h2, h3, h4, h5, h6 {
	color: #011d27;
}

h1 a, h2 a, h3 a, h4 a, h5 a, h6 a {
	font-weight: inherit;
}

h1 { font: 52px/56px "Oswald", sans-serif;}
h2 { font: 42px/46px "Oswald", sans-serif;}
h3 { font: 32px/36px "Oswald", sans-serif;}
h4 { font: 26px/30px "Oswald", sans-serif;}
h5 { font: 22px/26px "Oswald", sans-serif;}
h6 { font: 18px/22px "Oswald", sans-serif;}

.text-white { color: #fff !important; }
.text-dark { color: #322d2d !important;}

input[type="checkbox"] { display: inline; }

select,
textarea,
input[type="text"],
input[type="password"],
input[type="datetime"],
input[type="datetime-local"],
input[type="date"],
input[type="month"],
input[type="time"],
input[type="week"],
input[type="number"],
input[type="email"],
input[type="url"],
input[type="search"],
input[type="tel"],
input[type="color"],
.uneditable-input {
	color: #ccc;
}

textarea:focus,
input[type="text"]:focus,
input[type="password"]:focus,
input[type="datetime"]:focus,
input[type="datetime-local"]:focus,
input[type="date"]:focus,
input[type="month"]:focus,
input[type="time"]:focus,
input[type="week"]:focus,
input[type="number"]:focus,
input[type="email"]:focus,
input[type="url"]:focus,
input[type="search"]:focus,
input[type="tel"]:focus,
input[type="color"]:focus,
.uneditable-input:focus {
	color: #000;
	border-color: #bbb;
	-webkit-box-shadow: none;
        -moz-box-shadow: none;
	  	    box-shadow: none;
}

.colores-animation {
   -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
   filter: alpha(opacity=0);
   opacity: 0;
}
.colores-animation.animated {
   -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
   filter: alpha(opacity=100);
   opacity: 1;
}

/* bootstrap resetting elements */
.btn {
	background-image: none;
}

textarea, 
input[type="text"],
input[type="submit"],
input[type="password"], 
input[type="datetime"], 
input[type="datetime-local"], 
input[type="date"], 
input[type="month"], 
input[type="time"], 
input[type="week"], 
input[type="number"], 
input[type="email"], 
input[type="url"], 
input[type="search"], 
input[type="tel"], 
input[type="color"], 
.uneditable-input,
.dropdown-menu,
.navbar .nav > .active > a, 
.navbar .nav > .active > a:hover, 
.navbar .nav > .active > a:focus {
    -webkit-appearance: none;
	text-shadow: none;
	-webkit-box-shadow: none;
        -moz-box-shadow: none;
	       -o-box-shadow: none;
            box-shadow: none;
}

/* Top
-------------------------------------------------------------- */
.top {
    min-height: 2px;
    background-color: #fff;
	border-top: 4px solid #f29000;
}

.top ul.contact-info {
    margin: 10px 0;
    padding: 0;
}

.top ul.contact-info li {
    color: #fff;
    padding-left: 21px;
    margin-right: 27px;
    display: inline-block;
    position: relative;
}

.top ul.contact-info li a {
    color: #000;
}

.top ul.contact-info li:before {
    left: 0;
    top: 0;
    position: absolute;
    font-family: "FontAwesome";
    font-size: 15px;
    color: #011d27;
}

.top ul.contact-info li.phone:before {
    content: "\f095";
}

.top ul.contact-info li.email:before {
    content: "\f0e0";
    font-size: 12px;
}





.top-right-bar{ text-align:right; padding:10px 0 0 0}
input.top-search{ width:140px; padding:5px  !important; line-height:22px; height:25px; border:1px solid #005a9a; border-radius: 0  !important; font-size:12px;}
.top-select{ width:140px; padding:0; border:1px solid #f29000; margin:0 0 0 5px border-radius: 0 !important; height:25px; }






/* Header
-------------------------------------------------------------- */
.header {
    position: relative;
    background-color: #fff;
}

.header .logo {
    margin: 15px 0 15px 0;
    float: left;
    width: 170px;
}

/* Navigation
-------------------------------------------------------------- */
#mainnav {
    float: right;
}

#mainnav ul {
    list-style: none; 
    margin: 0;
    font-family: "Oswald";
    font-weight: 300;
    font-size: 20px;
}

#mainnav > ul > li {
    float: left;
}

#mainnav > ul > li > a {
    display: block; 
    line-height: 40px; 
    background: transparent; 
    color: #005a99;
    letter-spacing: 0.3px;
    padding-top: 10px;
    padding-bottom: 0px;
    padding-left: 29px;
    padding-right: 0;
 
}

#mainnav ul li ul li a { 
    display: block; 
    line-height: 36px;
    background-color: #011d27;
    padding: 6px 25px; 
    color: #fff;
}

#mainnav ul li {
    position: relative;
}

#mainnav ul li ul {
    width: 200px;
    position: absolute; 
    top: 100%; 
    left: 0; 
    opacity: 0; 
    visibility: hidden;
    padding-top: 5px;
    background-color: #f7941e;
}

#mainnav ul li ul li ul {
    left: 100%; 
    top: 0px;
}

#mainnav .sub-menu li a:hover {
    background-color: transparent;
}

.menu > li:hover:before {
    content: ""; 
    position: absolute; 
    left:50%;
    top:-3px;
   /* border-width: 9px 10px;
    border-color: #f7941e transparent transparent transparent;
    border-style: solid;*/
}

.menu2 > li:before {
    content: ""; 
    position: absolute; 
    left: 15px;
    bottom: -5px;
    height: 5px;
    width: 0;
    background-color: #f7941e;
    opacity: 0;

    -webkit-transition: all 0.3s ease-in-out;
      -moz-transition: all 0.3s ease-in-out;
       -ms-transition: all 0.3s ease-in-out;
        -o-transition: all 0.3s ease-in-out;
           transition: all 0.3s ease-in-out;
}

.menu2 > li:hover:before {
    opacity: 1;
    width: 100%;
}

#mainnav li:hover > ul {
    opacity: 1;
    visibility: visible;
    z-index: 999;
    -webkit-transform: translate(0, 0);
       -moz-transform: translate(0, 0);
        -ms-transform: translate(0, 0);
         -o-transform: translate(0, 0);
            transform: translate(0, 0);
}

#mainnav .sub-menu {
    -webkit-transition: all 0.4s ease;
       -moz-transition: all 0.4s ease;
        -ms-transition: all 0.4s ease;
         -o-transition: all 0.4s ease;
            transition: all 0.4s ease;
    -webkit-transform: translate(0px, 10px);
       -moz-transform: translate(0px, 10px);
        -ms-transform: translate(0px, 10px);
         -o-transform: translate(0px, 10px);
            transform: translate(0px, 10px);
}
#mainnav .sub-menu li a {
    -webkit-transition: padding 0.2s ease;
       -moz-transition: padding 0.2s ease;
        -ms-transition: padding 0.2s ease;
         -o-transition: padding 0.2s ease;
            transition: padding 0.2s ease;
}

#mainnav li:hover > .sub-menu > li > a { padding: 7px 25px;}


/* Mobile navigation
---------------------------------------- */
.btn-menu {
   font-size: 28px;
   font-family: "FontAwesome";
   color: #f7941e;
   float: right;
   text-align:center;
   width: 28px;
   height: 28px;
   margin-top: 15px;
   cursor: pointer;
   -webkit-transition: all .2s ease-out;
      -moz-transition: all .2s ease-out;
       -ms-transition: all .2s ease-out;
        -o-transition: all .2s ease-out;
           transition: all .2s ease-out;
}

.btn-menu:before{
    content: "\f0c9";
}

.btn-menu {
    display: none 
}
#mainnav-mobi {
   display: block;
   margin: 0 auto;
   width: 100%;
   position: absolute;
   background-color: #07232E;
   z-index: 1000;
}

#mainnav-mobi ul {
   display: block;
   list-style: none;
   margin: 0;
   padding: 0;
}

#mainnav-mobi ul li {
   margin:0;
   position: relative;
   text-align: left;
   border-top: 1px solid rgba(255, 255, 255, 0.9);
   cursor: pointer
}

#mainnav-mobi ul li:before {
  border: 0;
}

#mainnav-mobi ul > li > a {
   text-decoration: none;
   height: 50px;
   line-height: 50px;
   padding: 0 30px;
   color: #fff;
}

#mainnav-mobi ul.sub-menu {
   top: 100%;
   left: 0;
   z-index: 2000;
   position: relative;
   background-color: #00051C;
}

#mainnav-mobi > ul > li > ul > li,
#mainnav-mobi > ul > li > ul > li > ul > li {
   position: relative;
   border-top: 1px solid rgba(18, 209, 31, 0.3);
}

#mainnav-mobi > ul > li > ul > li > ul > li a {
   padding-left: 70px !important
}

#mainnav-mobi ul.sub-menu > li > a {
   display: block;
   text-decoration: none;
   padding: 0 60px;
   border-top-color: rgba(255,255,255,.1);
   -webkit-transition: all 0.2s ease-out;
      -moz-transition: all 0.2s ease-out;
        -o-transition: all 0.2s ease-out;
           transition: all 0.2s ease-out;
}

#mainnav-mobi > ul > li > ul > li:first-child a {
   border-top: none;
}

#mainnav-mobi ul.sub-menu > li > a:hover,
#mainnav-mobi > ul > li > ul > li.active > a {
   color: #fff;
}

.btn-submenu:before {
   content: "\f107";
   color: #fff;
   font-family: "FontAwesome";
   font-size: 20px;
   position: absolute;
   right: 40px;
   top: 15px;
}

.btn-submenu.active:before {
   content: "\f106"
}

/* Parallax
-------------------------------------------------------------- */
.parallax {
	width: 100%;
	background-attachment: fixed;
	background-position: 50% 0;
}

.parallax-bg1 {
	background-image: url(../images/parallax-bg1.html);
}

/* Page Title
-------------------------------------------------------------- */
.blog-page.page-title {
    text-align: center;
    background-image: url('../images/blog/bg.html');
    padding: 54px 0 53px 0;
    background-position: center;
}

.about-page.page-title {
    text-align: center;
    background:#a1abb2 url(../images/inner-banner/1_About-Us.jpg) bottom center no-repeat;
    padding: 94px 0 93px 0;
   
}

.about-page.page-title2 {
    text-align: center;
    background:#a1abb2 url(../images/inner-banner/2_Locations.jpg) bottom center no-repeat;
    padding: 94px 0 93px 0;
   
}

.about-page.page-title3 {
    text-align: center;
    background:#a1abb2 url(../images/inner-banner/3_CSR-Activity.jpg) bottom center no-repeat;
    padding: 94px 0 93px 0;
   
}

.about-page.page-title4 {
    text-align: center;
    background:#a1abb2 url(../images/inner-banner/4_Services.jpg) bottom center no-repeat;
    padding: 94px 0 93px 0;
   
}

.about-page.page-title5 {
    text-align: center;
    background:#a1abb2 url(../images/inner-banner/5_Quality.jpg) bottom center no-repeat;
    padding: 94px 0 93px 0;
   
}

.about-page.page-title6 {
    text-align: center;
    background:#a1abb2 url(../images/inner-banner/6_careers.jpg) bottom center no-repeat;
    padding: 94px 0 93px 0;
   
}

.about-page.page-title7 {
    text-align: center;
    background:#a1abb2 url(../images/inner-banner/7_Events.jpg) bottom center no-repeat;
    padding: 94px 0 93px 0;
   
}

.about-page.page-title8 {
    text-align: center;
    background:#a1abb2 url(../images/inner-banner/8_Contact-Us.jpg) bottom center no-repeat;
    padding: 94px 0 93px 0;
   
}






.contact-page.page-title {
    text-align: center;
    background-image: url('../images/contact/bg.html');
    padding: 54px 0 53px 0;
    background-position: center;
}

.page-title h4 {
    font-size: 24px;
    color: #fff;
    line-height: 24px;
    font-weight: 300;
    margin-top: -1px;
}

.page-title ul {
    margin: 0;
    padding: 0;
}

.page-title ul li {
    text-decoration: none;
    display: inline-block;
}


.about-bottom-box{}
.about-bottom-box h2{ font-size:26px; color:#f7941e}

h2.title1{ font-size:26px; color:#f7941e; margin:0; padding:0}
h2.title2{ font-size:26px; color:#f7941e; margin:0 0 20px 0 !important; padding:0}

.about-info h2{ font-size:26px; color:#f7941e}

/* Go Top Button
-------------------------------------------------------------- */
.go-top {
    position: fixed !important;
    right: 12px;
    bottom: -45px;
    background-color: #161625;
    color: #fff; 
    display: block;
    font-size: 22px;
    line-height: 35px;
    text-align: center;
    width: 35px;
    height: 35px;
    visibility: hidden;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    filter: alpha(opacity=0);
    opacity: 0;
    z-index: 9999;
    cursor: pointer;
    -webkit-border-radius: 50%;
      -moz-border-radius: 50%;
        -o-border-radius: 50%;
           border-radius: 50%;
    -webkit-transition: all 0.3s ease-in-out;
      -moz-transition: all 0.3s ease-in-out;
       -ms-transition: all 0.3s ease-in-out;
        -o-transition: all 0.3s ease-in-out;
           transition: all 0.3s ease-in-out;
}

.go-top:hover {
    color: #161625;
}

.go-top.show {
    bottom :4px;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
    filter: alpha(opacity=100);
    opacity: 1;
    visibility: visible;
}

.notification_error {
	color: #ff0000;
}

.head-slide.contact .note {
  text-align: left;
  margin-bottom: 15px;
}

.head-slide.contact .notification_error {
  color: #1DD329;
}

.head-slide.contact .notification_ok {
  color: #00f5ff;
}

/* Switcher
-------------------------------------------------------------- */
.switcher-container {
   width: 260px;
   position: fixed;
   right: -260px;
   top: 260px;
   z-index: 9999;
   background-color: #1dd329;
   z-index: 99999999;
}

.switcher-container h2 {
   color: #fff;
   font-size: 16px;
   font-weight: bold;
   height: 45px;
   line-height: 45px;
   padding-left: 20px;
   margin: 0;
}

.switcher-container h2 a {
   background-color: #120813;
   display: block;
   position: absolute;
   left: -45px;
   top: 0;
   width: 45px;
   height: 45px;
   line-height: 45px;
   text-align: center;
   outline: 0;
}

.switcher-container h2 a:hover {
   color: #fff;
}

.switcher-container h2 a:hover,
.switcher-container h2 a:focus {
   text-decoration: none;
}

.switcher-container h2 i {
   line-height: 45px;
   font-size: 20px;
}

.switcher-container h3 {
   font-size: 14px;
   font-weight: 700;
   color: #fff;
   margin: 0;
   line-height: 22px;
   margin-bottom: 15px;
}

.switcher-container .selector-box {
   background: #120813;
   color: #fff;
   overflow: hidden;
   padding: 10px 20px 20px;
}

.switcher-container .layout-switcher {
   margin: 0 0 10px 0;
   overflow: hidden;
}

.switcher-container .layout-switcher a.layout {
   float: left;
   display: block;
   cursor: pointer;
   text-align: center;
   font-weight: 700;
   padding: 10px 20px;
   margin-left: 10px;
}

.switcher-container .layout-switcher a.layout:first-child {
   margin-left: 0;
}

.switcher-container .layout-switcher a.layout:hover {
   color: #fff;
   cursor: pointer;
}

.switcher-container .color-box {
   height: auto;
   overflow: hidden;
   margin-bottom: 6px;
}

.switcher-container .styleswitch {
   width: 28px;
    height: 28px;
   display: block;
   cursor: pointer;
   margin: 0 8px 0 0;
   float: left;
}

.switcher-container .styleswitch {
    margin-bottom: 10px;
}
.switcher-container .styleswitch#color1 { background-color: #1dd329; }
.switcher-container .styleswitch#color2 { background-color: #18ded7; }
.switcher-container .styleswitch#color3 { background-color: #1f9353; }
.switcher-container .styleswitch#color4 { background-color: #df8316; }
.switcher-container .styleswitch#color5 { background-color: #d11267; }
.switcher-container .styleswitch#color6 { background-color: #d11212; }

.ui-element {
    margin-bottom: 50px;
}

.ui-element {
    font-weight: 500;
}

.ui-element p  {
    margin-bottom: 10px;
    font-size: 22px;
    line-height: 26px;
    color: #b7b7b7;
}

/* Media Queries
-------------------------------------------------------------- */
@media only screen and (min-width: 1930px) {
    .parallax {
        background-size: cover;
    }
}

/* Smaller than standard 1200 */
@media only screen and (max-width: 1199px) {
	
	
#mainnav > ul > li > a {
    display: block; 
    line-height: 50px;  
    color: #005a99;
 
    padding-top: 10px;
 
    padding-left: 15px;
 
}
    .roll-row .meta-bottom a.read-more {
        padding: 9px 7px;
        font-size: 16px;
    }

    .widget-flickr ul.flickr-photos li a {
        width: 65px;
        height: 65px;
        margin-right: 8px;
        margin-bottom: 8px;
    }

    .widget-flickr .flickr-photos {
        height: 160px;
    }

    .roll-row .titlebox .maintitle {
        font-size: 32px;
        line-height: 34px;
    }

    .footer-social span {
        font-size: 22px;
    }

    .footer-social .socials li a {
        width: 55px;
        height: 55px;
        line-height: 55px;
        font-size: 24px;
    }

    .post.post-details .categories {
        margin-bottom: 1px;
    }

    .post.post-details .meta-bottom .spacing {
        display: none;
    }

    .portfolio-container .portfolio-img {
        margin-bottom: 20px;
    }

    .testimonial-detail {
        width: 293px;
    }

    .about-persons {
        text-align: center;
    }

    .about-persons .persons.team-5 {
        width: 210px;
        margin-left: 0;
        margin-right: 20px;
        margin-bottom: 20px;
        display: inline-block;
    }

    .newsflash ul li {
        min-height: 163px;
    }

    .newsflash .post-overlay.bottom:before {
        margin-left: -15px;
    }

    .newsflash.large ul li {
        width: 22.37%;
        min-height: 217px;
    }

    .newsflash.large .post-overlay.bottom:before {
        bottom: 22%;
        margin-left: -20px;
        width: 40px;
        height: 40px;
        background-size: 40px 40px;
    }

    .logo-client.logo-small .bx-controls {
        width: 940px;
    }

    .head-slide .control:before {
        width: 940px;
        top: -20px;
        margin-left: -470px;
        background-position: center;
    }
}

/* Smaller than standard 980 */
@media only screen and (max-width: 979px) {
	
	 .roll-row .about-teams .about-img {
    position: relative;
    width: 90%; margin: 0  auto; float: none 
}

.roll-row .about-teams .about-info { width:98%; float: none; margin:0 auto}
	
    .parallax {
        background-size: cover;
        background-attachment: scroll;
        background-position: top center!important;
    }

    .btn-menu {
        display: block ;
    }

    .roll-row .titlebox .maintitle {
        font-size: 25px;
        line-height: 27px;
    }

    .roll-row .titlebox .subtitle {
        font-size: 20px;
        line-height: 22px;
    }

    .roll-row .menu-items .text-search {
        display: none;
    }

    .roll-row .menu-items ul.right-items li {
        float: left;
    }

    .roll-row .meta-bottom .categories {
        margin-top: 1px;
        margin-left: 0;
    }

    .roll-row .meta-bottom a.read-more {
        padding: 10px 22px;
    }

    .widget-flickr ul.flickr-photos li a {
        width: 50px;
        height: 50px;
        margin-right: 5px;
        margin-bottom: 5px;
    }

    .widget-flickr .flickr-photos {
        height: 127px;
    }

    .footer-social span {
        font-size: 20px;
    }

    .footer-social .socials li a {
        width: 40px;
        height: 40px;
        line-height: 40px;
        font-size: 20px;
    }

    .widget-newsletter button.btn-colores {
        font-size: 15px;
        padding: 10px;
    }

    .roll-row .entry-post blockquote {
        margin-bottom: 20px;
        margin-left: 10px;
        margin-right: 0;
    }

    .post .avatar {
        float: none;
        display: inline-block;
        width: 110px;
        margin-bottom: 5px;
    }

    .post .info {
        width: 100%;
        padding-right: 20px;
    }

    .comment .comment-author {
        position: relative;
        display: inline-block;
        width: 100%;
        margin-bottom: 5px;
    }

    .comments-list ul li ul.children {
        margin-left: 20px;
    }

    .comment .comment-text {
        margin-left: 0;
    }

    .comment-form .input-wrap {
        margin-left: 0%;
		width: 100%;
		float: none;
    }

    .contact-form .input-wrap {
        margin-left: 0%;
    }

    .roll-row .post-overlay.bottom:before {
        background-size: 40px 40px;
        width: 40px;
        height: 40px;
    }

    .portfolio-container .span3 .post-overlay.bottom:before {
        bottom: 14%;
    }

    .roll-row .post-overlay.bottom h5 {
        font-size: 22px;
    }

    .testimonial-detail {
        width: 347px;
    }
 

    .roll-row.portfolio-posts .featured-post {
        padding-top: 0;  
    }

    .head-slide .slide2 img {
        width: 45%;
    }

    .head-slide .slide2 .slide-right {
        width: 400px;
        bottom: 15%;
    }

    .slide2 .slide-right h3 {
        font-size: 29px;
    }

    .slide2 .slide-right p.content {
        font-size: 12px;
    }

    .head-slide .slide img {
      
    }

    .slide .slide-left {
        width: 400px;
		top: 5%;
 
    }

    .slide .slide-left h3.title {
        font-size: 26px;
    }

    .slide-left p.content {
        font-size: 12px;
    }

    .slide-left h3.sub-title {
        font-size: 26px;
    }

    .head-slide .slide3 img {
        width: 45%;
    }

    .head-slide .slide3 .slide-right {
        width: 400px;
        bottom: 12%;
    }

    .slide3 .slide-right h3.title {
        font-size: 26px;
    }

    .slide3 .slide-right p.content {
        font-size: 12px;
    }

    .slide3 .slide-right h3.sub-title {
        font-size: 26px;
    }

    .newsflash ul li {
        width: 30%;
        min-height: 211px;
        margin-bottom: 3%;
        margin-right: 3%;
        margin-left: 0;
    }

    .newsflash .post-overlay.bottom:before {
        margin-left: -25px;
    }

    .newsflash.large ul li {
        width: 45%;
        margin-left: 0;
        margin-right: 3%;
        min-height: 325px;
    }

    .newsflash.large .post-overlay.bottom:before {
        bottom: 22%;
        margin-left: -25px;
        width: 50px;
        height: 50px;
        background-size: 50px 50px;
    }

    .box-services {
        margin-bottom: 5px;
    }

    .logo-client.logo-small .bx-controls {
        width: 724px;
    }

    .head-slide .control:before {
          width: 724px;
          top: -5px;
          margin-left: -362px;
    }

    .head-slide .slide .slide-left {
        width: 480px;
        z-index: 1;
    }

    .slide.contact .contact-form {
        width: 460px;
    }

    .slide.contact .input-wrap.name {
       
    }

    .slide.contact .input-wrap.email {
       
    }

    /*

    .head-slide .slide .slide-left {
        width: 300px;
    }

    .slide.contact .contact-form {
        width: 100%;
    }

    .slide.contact .input-wrap.name,
    .slide.contact .input-wrap.email {
        width: 100%;

    }*/
}

/* Tablet Landscape */
 @media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : landscape) {
    .parallax {
        background-size: cover;
        background-attachment: scroll;
        background-position: top center!important;
    }
}

/* Tablet Portrait Size */
@media only screen and (min-width: 768px) and (max-width: 959px) {

}


@media only screen and (max-width: 800px) { 


	}


/* All Mobile Sizes */
@media only screen and (max-width: 767px) {
    body {
        padding: 0;
    }

    .header .logo {
        margin-left: 30px;
    }


    .top ul.contact-info {
        padding-left: 30px;
    }

    .btn-menu {
        margin-right: 30px;
    }

    .team-colores .gallery .slider ul.slides li {
        text-align: center;
    }

    .team-colores .about-img {
        float: none !important;
        min-height: 0 !important;
        display: inline-block;
        overflow: hidden;
    }

    .team-colores .about-teams .about-info {
        float: none;
        width: 100%;
        min-height: 0;
        padding: 0 30px;
    }

    .team-colores .about-info h5.about-name {
        margin: 0 !important;
    }

    .team-colores .about-info p.about-regency {
        text-align: center;
    }

    .team-colores .about-info p {
        text-align: left;
    }

    .roll-row .titlebox .maintitle {
        font-size: 19px;
        line-height: 25px;
    }

    .roll-row .titlebox .subtitle {
        font-size: 16px;
        line-height: 25px;
    }

    .roll-row.blog-posts .widget:first-child {
        margin-top: 10px;
    }

    .widget-flickr ul.flickr-photos li a {
       margin-right: 15px;
        margin-bottom: 15px;
        width: 75px;
        height: 75px;
    }

    .widget-flickr .flickr-photos {
        height: auto;
    }

    .roll-row .menu-items ul.left-items li a {
        padding: 10px;
    }

    .roll-row .menu-items ul.right-items {
        height: 40px;
    }

    .roll-row .menu-items ul.left-items li {
        font-size: 18px;
    }

    .roll-row .menu-items .button-search {
        width: 40px;
        height: 40px;
    }

    .roll-row .menu-items .button-search:before {
        font-size: 16px;
        padding: 10px 11px;
    }

    .footer-about .widget-brand.widget-over {
        margin-top: 0;
    }

    .footer-social .socials {
        text-align: center;
    }

    .footer-social span {
        margin-top: 5px;
    }

    .footer-social ul li {
        margin-top: 1px;
    }

    .roll-row .meta-bottom .share-post ul {
        margin-top: 1px;
    }

    .roll-row .meta-bottom a.read-more {
        margin-top: 1px;
        padding: 9px 7px;
        font-size: 14px;
    }

    .roll-row .meta-bottom .categories {
        margin-top: 0;
    }

    .roll-row .meta-bottom .share-post {
        margin-bottom: 1px;
    }

    .roll-row .meta-bottom .share-post ul {
        margin-top: 0;
    }

    .widget-newsletter button.btn-colores {
        font-size: 18px;
        padding: 10px 25px;
    }

    .team-colores .carousel {
        height: auto;
        opacity: 1;
    }

    .roll-row .entry-post blockquote {
        margin-bottom: 0;
        padding: 15px;
    }

    .comment-form .input-wrap {
        margin-left: 0%;
        width: 100%;
      
    }

    .comment-form .input-wrap.website {
        width: 100%;
        margin-left: 0;
    }

    .contact-form .input-wrap {
        margin-left: 0%;
      
    }

    .contact-form .input-wrap.phone {
        margin-left: 0;
    }

    .portfolio-container .span3 {
        width: 32% !important;
        margin-left: 1%;
    }

    .portfolio-container .span4 {
        width: 48% !important;
        margin-left: 1%;
        margin-right: 1px;
    }

    .portfolio-container .portfolio-img {
        margin-bottom: 5%;
    }

    .roll-row .span4 .post-overlay.bottom:before {
        background-size: 50px 50px;
        width: 50px;
        height: 50px;
    }

    .portfolio-container .span4 .post-overlay.bottom:before {
        bottom: 28.92%;
    }

    .roll-row.portfolio-posts .about-img {
        float: none;
        width: 100%;
        margin-bottom: 20px;
        min-height: 0;
    }

    .roll-row.portfolio-posts .about-info {
        width: 100%;
        float: none;
        padding-left: 0;
    }

    .about-teams .tabs {
        margin-bottom: 10px;
    }

    .roll-row .about-teams .about-img {
        min-height: 0;
        width: 98%;
    }

    

    .roll-row .about-teams .about-info h5.about-name {
        margin-top: 0;
    }

    .box-error {
        width: 100%;
    }

    .box-error h1 {
        width: 100%;
        font-size: 200px;
    }

    .box-error h2 {
        width: 100%;
        font-size: 100px;
    }

    .roll-row.error-posts {
        overflow: hidden;
    }

    .head-slide .slide2 .slide-right {
        width: 50%;
    }

    .slide2 .slide-right p.content {
        top: 90px;
    }

    .head-slide .slide2 .slide-right {
        min-height: 280px;
    }

    .head-slide .control h5.title {
        font-size: 18px;
        line-height: 20px;
    }

    .head-slide .control p.sub-title {
        font-size: 10px;
        line-height: 12px;
        width: 100%;
        padding: 0 100px;
    }

    .head-slide .slide2,
    .head-slide .slide2 ul li {
        height: 470px;
    }

    .head-slide {
       
    }

    .head-slide .slide,
    .head-slide .slide ul li {
     
    }

    .head-slide .slide .slide-left {
      
    }

    .slide .slide-left h3.title,
    .slide .slide-left h3.sub-title {
        font-size: 22px;
        line-height: 30px;
    }

    .head-slide .slide3,
    .head-slide .slide3 ul li {
        height: 470px;
    }

    .head-slide .slide3 .slide-right {
        width: 50%;
        min-height: 330px;
    }

    .slide3 .slide-right h3.title,
    .slide3 .slide-right h3.sub-title {
        font-size: 22px;
        line-height: 30px;
    }

    .purchase .purchase-left { 
        width: 100%;
        margin-bottom: 20px;
    }

    .purchase .purchase-right {
        width: 30%;
        margin: 0 auto;
        float: none;
        display: inherit;
    }

    .newsflash ul li {
        width: 45%;
        min-height: 336px;
    }

    .newsflash.large ul li {
        width: 320px;
        margin-left: 0;
        margin-right: 30px;
        min-height: 320px;
    }

    .newsflash.large .post-overlay.bottom:before {
        bottom: 22%;
        margin-left: -25px;
        width: 50px;
        height: 50px;
        background-size: 50px 50px;
    }

    .roll-row.what-colores .experts {
        margin-bottom: 10px;
    }

    .logo-client.logo-small .bx-controls {
        width: 100%;
    }

    .head-slide .control {
        overflow: hidden;
    }

    .head-slide .slide .slide-left {
      
    }

    .head-slide.contact img {
        display: none;
    }

    .about-persons .persons.team-4,
    .about-persons .persons.team-3 {
        width: 210px;
        margin-right: 20px;
        margin-left: 0;
        margin-bottom: 20px;
        display: inline-block;
    }
}

/* Mobile Landscape Size */
@media only screen and (min-width: 480px) and (max-width: 767px) {
}

/* Mobile Portrait Size */
@media only screen and (max-width: 479px) {
	
	.img-box{ float:none; width:100%}
.img-box img{ width:100%}
.txt-box{ margin-right:0%; float:none; width:100%}
.txt-box p{ color:#fff}
	
	
    .top ul.contact-info {
        display: none;
    }

    .team-colores .post-overlay-trans {
        border-width: 0px;
    }

    .roll-row .menu-items ul.left-items li {
        font-size: 10px;
    }

    .roll-row .meta-bottom .categories {
        width: 100%;
    }

    .roll-row .meta-bottom .categories li {
        margin-top: 1px;
    }

    .roll-row .meta-bottom .share-post {
        width: 100%;
        text-align: center;
    }

    .team-colores .carousel {
        height: 0;
        opacity: 0;
    }

    .team-colores .sub-carousel {
        height: auto;
        opacity: 1;
    }

    .portfolio-projects .sub-carousel {
        height: auto;
        opacity: 1;
    }

    .about-posts .sub-carousel {
        height: auto;
        opacity: 1;
    }

    .about-posts .carousel {
        height: 0;
        opacity: 0;
    }

    .portfolio-projects .carousel {
        height: 0;
        opacity: 0;
    }

    .post .info {
        padding: 0;
    }

    .roll-row .entry-post blockquote {
        width: 100%;
        margin: 0;
        margin-bottom: 15px;
    }

    .comment-form .input-wrap {
        margin-left: 0;
        width: 100%;
    }

    .contact-form .input-wrap {
        margin-left: 0;
        width: 100%;
    }

    .portfolio-container .portfolio-img {
        margin-bottom: 5%;
    }

    .portfolio-container .span3 {
        width: 48% !important;
        margin-left: 1%;
        margin-right: 1%;
    }

    .roll-row .span4 .post-overlay.bottom:before {
        background-size: 40px 40px;
        width: 40px;
        height: 40px;
    }

    .portfolio-container .span4 .post-overlay.bottom:before {
        bottom: 14%;
    }

    .testimonial-detail {
        width: 295px;
    }

    .box-error h1 {
        font-size: 100px;
        line-height: 100px;
    }

    .box-error h2 {
        font-size: 50px;
        line-height: 50px;
    }

    body.error-page .logo {
        margin: 20px auto;
    }

    .roll-row.error-posts {
        padding: 10px 0 0 !important;
    }

    .head-slide.video-slide {
        margin-top: 0 !important;
        height: 167px !important;
    }

    .head-slide.video-slide + .roll-row {
        padding-top: 0;
    }

    .header {
        background-color: #fff !important;
    }

    .head-slide .slide2 img {
        display: none;
    }

    .head-slide .slide2 .slide-right {
        width: 100%;
    }

    .head-slide .control button {
        width: 20px;
        height: 20px;
        top: 40px;
    }

    .head-slide .control button.fa-angle-left {
        left: 0;
    }

    .head-slide .control button.fa-angle-right {
        right: 0;
    }

    .head-slide .slide2,
    .head-slide .slide2 ul li {
        height: 360px !important;
    }

    .head-slide {
 
    }

    .head-slide .control button:before {
        font-size: 12px;
    }

    .head-slide .control h5.title {
        font-size: 15px;
        line-height: 18px;
        padding: 21px 20px 9px 20px;
    }

    .head-slide .control p.sub-title {
        padding: 0 20px;
    }

    .head-slide .slide img {
      
    }

    .head-slide .slide .slide-left {
        width: 100%;
    }

    .slide .slide-left h3.title, 
    .slide .slide-left h3.sub-title {
        padding-left: 0;
        font-size: 20px;
    }
.head-slide{ position:relative !important}
    .head-slide .slide,
    .head-slide .slide ul li { 
		position:relative !important;
    }

    .head-slide .slide .slide-left {
  
    }

    .head-slide .slide3 img {
        display: none;
    }

    .head-slide .slide3 .slide-right {
        width: 100%;
    }

    .slide3 .slide-right h3.title, 
    .slide .slide-right h3.sub-title {
        padding-left: 0;
        font-size: 20px;
    }

    .head-slide .slide3,
    .head-slide .slide3 ul li {
        height: 410px !important;
    }

    .head-slide .slide3 .slide-right {
        min-height: 330px;
        bottom: 10%;
    }

    .purchase .purchase-left { 
        width: 100%;
    }

    .purchase .purchase-right {
        width: 100%;
    }

    .purchase .purchase-left .title {
        margin-bottom: 20px;
    }

    .purchase .purchase-left .content {
        margin-bottom: 15px;
        display: inline-block;
    }

    .newsflash ul li {
        width: 100%;
        min-height: 310px;
        display: inline-block;
    }

    .newsflash .img {
        width: auto;
    }

    .newsflash.large ul li {
        width: 100%;
        margin-left: 0;
        margin-right: 0;
        min-height: 310px;
    }

    .footer-social span {
        width: 100%;
        padding: 0;
    }

    .header .logo {
        margin: 15px 0;
        margin-left: 20px;
		width:180px;
    }

    .header .logo img {
        width: 100%;
    }

    .btn-menu {
        margin-right: 20px;
        font-size: 20px;
        margin-top: 20px;
    }

    .logo-client.logo-small .client {
        width: 104px;
    }

    .gallery .sub-carousel ul.slides li img {
        height: 170px;
    }

    .roll-row .about-teams .carousel {
        margin: 0;
    }

    .roll-row .about-teams .sub-carousel {
        margin-top: 55px;
    }
	

    .price-box .price-table {
        margin-top: 20px;
    }

    .roll-row.services-posts .box-services {
        margin-top: 20px;
    }

    .head-slide.contact {
        height: 575px !important;
    }

    .head-slide.contact .slide-left {
        position: relative;
        top: 0;
        left: 0;
        height: 575px;
    }

    .slide.contact .contact-form {
        width: 100%;
    }

    .slide.contact .input-wrap.name,
    .slide.contact .input-wrap.email {
        width: 100%;

    }

    .about-persons .persons.team-5,
    .about-persons .persons.team-4,
    .about-persons .persons.team-3 {
    	float: none;
    }
	
	.slide-left{}
	.slide .slide-left h3.title, .slide .slide-left h3.sub-title{ line-height:28px; font-size:20px; margin:0; padding: 5px 5px 5px  }
	.head-slide .slide .slide-left{ left:2%; top:3%; height: auto !important}
}
 
/* Retina Devices */
@media only screen and (min-device-pixel-ratio: 2), only screen and (min-resolution: 192dpi), only screen and (min-resolution: 2dppx) {

}

.buttom-address{ margin-top:40px;}