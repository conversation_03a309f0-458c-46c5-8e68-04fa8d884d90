<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Events & Exhibitions | Elmia Subcontractor 2019 - SUYASH</title>

    <!-- SEO Meta Tags -->
    <meta name="description" content="SUYASH participation in international trade shows and exhibitions. Featured at Elmia Subcontractor 2019 in Sweden, showcasing precision engineering capabilities to global markets.">
    <meta name="keywords" content="trade shows, exhibitions, Elmia Subcontractor 2019, international events, manufacturing exhibitions, precision engineering showcase, global markets, Sweden exhibition">
    <meta name="author" content="SUYASH Manufacturing">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="https://www.suyashmanufacturing.com/events.html">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://www.suyashmanufacturing.com/events.html">
    <meta property="og:title" content="Events & Exhibitions | Elmia Subcontractor 2019 - SUYASH">
    <meta property="og:description" content="SUYASH participation in international trade shows including Elmia Subcontractor 2019 in Sweden, showcasing precision engineering capabilities.">
    <meta property="og:image" content="https://www.suyashmanufacturing.com/images/elmia-2019.png">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://www.suyashmanufacturing.com/events.html">
    <meta property="twitter:title" content="Events & Exhibitions | Elmia Subcontractor 2019 - SUYASH">
    <meta property="twitter:description" content="SUYASH participation in international trade shows including Elmia Subcontractor 2019 in Sweden.">
    <meta property="twitter:image" content="https://www.suyashmanufacturing.com/images/elmia-2019.png">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Custom Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#f26722',
                        secondary: '#0a0a0a',
                        accent: '#e05310',
                        gray: {
                            50: '#f9fafb',
                            100: '#f3f4f6',
                            200: '#e5e7eb',
                            300: '#d1d5db',
                            400: '#9ca3af',
                            500: '#6b7280',
                            600: '#4b5563',
                            700: '#374151',
                            800: '#1f2937',
                            900: '#111827',
                        }
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                        'display': ['Poppins', 'system-ui', 'sans-serif'],
                    },
                    animation: {
                        'fade-in-up': 'fadeInUp 0.6s ease-out',
                        'fade-in-down': 'fadeInDown 0.6s ease-out',
                        'fade-in-left': 'fadeInLeft 0.6s ease-out',
                        'fade-in-right': 'fadeInRight 0.6s ease-out',
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'bounce-slow': 'bounce 2s infinite',
                    },
                    keyframes: {
                        fadeInUp: {
                            '0%': { opacity: '0', transform: 'translateY(30px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        },
                        fadeInDown: {
                            '0%': { opacity: '0', transform: 'translateY(-30px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        },
                        fadeInLeft: {
                            '0%': { opacity: '0', transform: 'translateX(-30px)' },
                            '100%': { opacity: '1', transform: 'translateX(0)' }
                        },
                        fadeInRight: {
                            '0%': { opacity: '0', transform: 'translateX(30px)' },
                            '100%': { opacity: '1', transform: 'translateX(0)' }
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="font-sans text-gray-900 bg-white overflow-x-hidden">
    <!-- Loading Screen -->
    <div id="loader" class="fixed inset-0 z-50 flex items-center justify-center bg-white transition-opacity duration-500">
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-4 border-primary border-t-transparent"></div>
            <p class="mt-4 text-gray-600 font-medium">Loading Excellence...</p>
        </div>
    </div>

    <!-- Navigation Header -->
    <header id="header" class="fixed top-0 left-0 right-0 z-40 bg-white/95 backdrop-blur-md border-b border-gray-100 transition-all duration-300">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-20">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <a href="index.html" class="flex items-center space-x-3">
                        <img src="images/logo.png" alt="SUYASH" class="h-12 w-auto transition-transform duration-300 hover:scale-105" />
                        
                    </a>
                </div>
                
                <!-- Desktop Navigation -->
                <nav class="hidden lg:flex items-center space-x-8">
                    <a href="index.html" class="text-gray-700 hover:text-primary transition-colors duration-200 font-medium">Home</a>
                    <div class="relative group">
                        <button class="flex items-center space-x-1 text-gray-700 hover:text-primary transition-colors duration-200 font-medium">
                            <span>About</span>
                            <svg class="w-4 h-4 transition-transform duration-200 group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div class="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-100 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                            <div class="py-2">
                                <a href="history.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors duration-200">History</a>
                                <a href="locations.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors duration-200">Locations</a>
                                <a href="csr-activities.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors duration-200">CSR Activities</a>
                            </div>
                        </div>
                    </div>
                    <a href="services.html" class="text-gray-700 hover:text-primary transition-colors duration-200 font-medium">Services</a>
                    <a href="quality.html" class="text-gray-700 hover:text-primary transition-colors duration-200 font-medium">Quality</a>
                    <a href="careers.html" class="text-gray-700 hover:text-primary transition-colors duration-200 font-medium">Careers</a>
                    <a href="events.html" class="text-primary font-semibold border-b-2 border-primary pb-1">Events</a>
                    <a href="contact-us.html" class="bg-primary text-white px-6 py-2 rounded-full hover:bg-accent transition-colors duration-200 font-medium">Contact Us</a>
                </nav>
                
                <!-- Mobile Menu Button -->
                <button id="mobileMenuToggle" class="lg:hidden flex flex-col items-center justify-center w-8 h-8 space-y-1.5">
                    <span class="w-6 h-0.5 bg-gray-700 transition-all duration-300"></span>
                    <span class="w-6 h-0.5 bg-gray-700 transition-all duration-300"></span>
                    <span class="w-6 h-0.5 bg-gray-700 transition-all duration-300"></span>
                </button>
            </div>
        </div>
        
        <!-- Mobile Navigation Menu -->
        <div id="mobileMenu" class="lg:hidden fixed inset-x-0 top-20 bg-white border-b border-gray-100 shadow-lg transform -translate-y-full opacity-0 transition-all duration-300">
            <div class="px-4 py-6 space-y-4">
                <a href="index.html" class="block text-gray-700 hover:text-primary transition-colors duration-200">Home</a>
                <div class="space-y-2">
                    <p class="text-gray-900 font-medium">About</p>
                    <div class="pl-4 space-y-2">
                        <a href="history.html" class="block text-gray-600 hover:text-primary transition-colors duration-200">History</a>
                        <a href="locations.html" class="block text-gray-600 hover:text-primary transition-colors duration-200">Locations</a>
                        <a href="csr-activities.html" class="block text-gray-600 hover:text-primary transition-colors duration-200">CSR Activities</a>
                    </div>
                </div>
                <a href="services.html" class="block text-gray-700 hover:text-primary transition-colors duration-200">Services</a>
                <a href="quality.html" class="block text-gray-700 hover:text-primary transition-colors duration-200">Quality</a>
                <a href="careers.html" class="block text-gray-700 hover:text-primary transition-colors duration-200">Careers</a>
                <a href="events.html" class="block text-primary font-semibold">Events</a>
                <a href="contact-us.html" class="block bg-primary text-white px-6 py-3 rounded-full text-center hover:bg-accent transition-colors duration-200">Contact Us</a>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="relative bg-gradient-to-br from-gray-900 to-gray-800 text-white pt-32 pb-20">
        <div class="absolute inset-0 bg-black/20"></div>
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-5xl lg:text-6xl font-bold mb-6">
                <span class="text-primary">Events</span> & Exhibitions
            </h1>
            <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                Showcasing our innovations and connecting with industry leaders worldwide
            </p>
        </div>
    </section>

    <!-- Events Content Section -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <span class="inline-block px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-semibold mb-4">
                    Industry Participation
                </span>
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">Past Events</h2>
                <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                    SUYASH actively participates in major industry exhibitions and trade shows to showcase our capabilities and connect with global partners.
                </p>
            </div>
            
            <!-- Featured Event -->
            <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden mb-12">
                <div class="grid lg:grid-cols-2 gap-8">
                    <!-- Event Details -->
                    <div class="p-8 lg:p-12">
                        <div class="mb-6">
                            <span class="inline-block px-3 py-1 bg-primary/10 text-primary rounded-full text-sm font-semibold mb-4">
                                Featured Event
                            </span>
                            <h3 class="text-2xl lg:text-3xl font-bold text-gray-900 mb-4">Elmia Subcontractor 2019</h3>
                            <p class="text-gray-600 leading-relaxed mb-6">
                                SUYASH participated in the prestigious Elmia Subcontractor exhibition, one of Europe's leading trade fairs for subcontracting and manufacturing. This event provided an excellent platform to showcase our precision engineering capabilities to international clients.
                            </p>
                        </div>
                        
                        <!-- Event Info -->
                        <div class="space-y-4 mb-8">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <p class="font-semibold text-gray-900">Stand No.: 32</p>
                                    <p class="text-gray-600">Hall D03</p>
                                </div>
                            </div>
                            
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <p class="font-semibold text-gray-900">12th - 15th November 2019</p>
                                    <p class="text-gray-600">Jönköping, Sweden</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Event Logo -->
                        <div class="bg-gray-50 rounded-lg p-6 text-center">
                            <img src="images/elmia-2019.png" alt="Elmia Subcontractor 2019" class="max-w-full h-auto mx-auto" style="max-height: 120px;">
                        </div>
                    </div>
                    
                    <!-- Event Image -->
                    <div class="lg:p-8">
                        <div class="h-full bg-gray-50 rounded-lg overflow-hidden">
                            <img src="images/reduce-costs-elma-newsletter-2019.jpg" alt="Elmia Subcontractor 2019 Newsletter" class="w-full h-full object-cover">
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Event Benefits -->
            <div class="grid md:grid-cols-3 gap-8 mb-12">
                <div class="bg-gray-50 rounded-2xl p-6 text-center hover:shadow-lg transition-shadow duration-300">
                    <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">Global Reach</h3>
                    <p class="text-gray-600">Connecting with international clients and expanding our global presence</p>
                </div>
                
                <div class="bg-gray-50 rounded-2xl p-6 text-center hover:shadow-lg transition-shadow duration-300">
                    <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">Innovation</h3>
                    <p class="text-gray-600">Showcasing our latest technological capabilities and innovations</p>
                </div>
                
                <div class="bg-gray-50 rounded-2xl p-6 text-center hover:shadow-lg transition-shadow duration-300">
                    <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">Networking</h3>
                    <p class="text-gray-600">Building valuable partnerships with industry leaders and suppliers</p>
                </div>
            </div>
            
            <!-- Call to Action -->
            <div class="bg-gradient-to-br from-primary to-accent rounded-2xl p-8 text-white text-center">
                <div class="max-w-2xl mx-auto">
                    <h3 class="text-2xl font-bold mb-4">Meet Us at Future Events</h3>
                    <p class="text-lg text-white/90 mb-6">
                        Stay updated on our upcoming exhibitions and trade show participations. Connect with our team to explore partnership opportunities.
                    </p>
                    <a href="contact-us.html" class="inline-flex items-center bg-white text-primary px-8 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors duration-300">
                        Get In Touch
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gradient-to-br from-gray-900 to-black text-white">
        <!-- Main Footer Content -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <div class="grid lg:grid-cols-4 md:grid-cols-2 gap-8">
                <!-- Company Info -->
                <div class="lg:col-span-2">
                    <div class="flex items-center space-x-3 mb-6">
                        <img src="images/logo.png" alt="SUYASH" class="h-12 w-auto" />

                    </div>
                    <p class="text-gray-300 leading-relaxed mb-6 max-w-md">
                        Leading manufacturer of precision engineering components, serving global markets with innovative solutions and uncompromising quality.
                    </p>
                    <div class="flex space-x-4">
                        <!-- <a href="#" class="w-10 h-10 bg-primary/20 hover:bg-primary rounded-full flex items-center justify-center transition-colors duration-300">
                            <img src="images/f-icon.png" alt="Facebook" class="w-5 h-5">
                        </a> -->
                        <a href="https://www.linkedin.com/company/suyash-impex-pvt-ltd/" class="w-10 h-10 bg-primary/20 hover:bg-primary rounded-full flex items-center justify-center transition-colors duration-300">
                            <img src="images/in-icon.png" alt="LinkedIn" class="w-5 h-5">
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="text-lg font-semibold mb-6">Quick Links</h4>
                    <ul class="space-y-3">
                        <li><a href="index.html" class="text-gray-300 hover:text-primary transition-colors duration-200">Home</a></li>
                        <li><a href="history.html" class="text-gray-300 hover:text-primary transition-colors duration-200">History</a></li>
                        <li><a href="services.html" class="text-gray-300 hover:text-primary transition-colors duration-200">Services</a></li>
                        <li><a href="quality.html" class="text-gray-300 hover:text-primary transition-colors duration-200">Quality</a></li>
                        <li><a href="careers.html" class="text-gray-300 hover:text-primary transition-colors duration-200">Careers</a></li>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div>
                    <h4 class="text-lg font-semibold mb-6">Get In Touch</h4>
                    <ul class="space-y-3">
                        <li><a href="events.html" class="text-primary font-medium">Events</a></li>
                        <li><a href="contact-us.html" class="text-gray-300 hover:text-primary transition-colors duration-200">Contact Us</a></li>
                        <li><a href="locations.html" class="text-gray-300 hover:text-primary transition-colors duration-200">Our Locations</a></li>
                        <li><a href="csr-activities.html" class="text-gray-300 hover:text-primary transition-colors duration-200">CSR Activities</a></li>
                    </ul>

                    <!-- CTA Button -->
                    <div class="mt-6">
                        <a href="contact-us.html" class="inline-flex items-center bg-primary hover:bg-accent text-white px-6 py-3 rounded-full font-semibold transition-all duration-300 transform hover:scale-105">
                            Start Your Project
                            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer Bottom -->
        <div class="border-t border-gray-800">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-gray-400 text-sm">
                        All Right Reserved Suyash 2015 | Developed by : <a href="#" class="text-primary hover:text-accent transition-colors duration-200">SV</a>
                    </p>
                    <div class="flex items-center space-x-6 mt-4 md:mt-0">
                        <span class="text-gray-400 text-sm">Follow us</span>
                        <div class="flex space-x-3">
                            <!-- <a href="#" class="text-gray-400 hover:text-primary transition-colors duration-200">
                                <img src="images/f-icon.png" alt="Facebook" class="w-4 h-4">
                            </a> -->
                            <a href="https://www.linkedin.com/company/suyash-impex-pvt-ltd/" class="text-gray-400 hover:text-primary transition-colors duration-200">
                                <img src="images/in-icon.png" alt="LinkedIn" class="w-4 h-4">
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scroll to Top Button -->
    <button id="goTop" class="fixed bottom-8 right-8 w-12 h-12 bg-primary hover:bg-accent text-white rounded-full shadow-lg opacity-0 invisible transition-all duration-300 transform hover:scale-110 z-50">
        <svg class="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
        </svg>
    </button>

    <!-- JavaScript -->
    <script>
        // Mobile Menu Toggle
        class MobileMenu {
            constructor() {
                this.toggle = document.getElementById('mobileMenuToggle');
                this.menu = document.getElementById('mobileMenu');
                this.isOpen = false;
                this.init();
            }

            init() {
                this.toggle.addEventListener('click', () => this.toggleMenu());
                document.addEventListener('click', (e) => {
                    if (!this.toggle.contains(e.target) && !this.menu.contains(e.target)) {
                        this.closeMenu();
                    }
                });
            }

            toggleMenu() {
                this.isOpen = !this.isOpen;
                this.updateMenu();
            }

            closeMenu() {
                this.isOpen = false;
                this.updateMenu();
            }

            updateMenu() {
                const spans = this.toggle.querySelectorAll('span');
                if (this.isOpen) {
                    this.menu.style.transform = 'translateY(0)';
                    this.menu.style.opacity = '1';
                    spans[0].style.transform = 'rotate(45deg) translate(5px, 5px)';
                    spans[1].style.opacity = '0';
                    spans[2].style.transform = 'rotate(-45deg) translate(7px, -6px)';
                } else {
                    this.menu.style.transform = 'translateY(-100%)';
                    this.menu.style.opacity = '0';
                    spans.forEach(span => {
                        span.style.transform = 'none';
                        span.style.opacity = '1';
                    });
                }
            }
        }

        // Scroll Effects
        class ScrollEffects {
            constructor() {
                this.header = document.getElementById('header');
                this.goTopBtn = document.getElementById('goTop');
                this.init();
            }

            init() {
                window.addEventListener('scroll', () => this.handleScroll());
                this.goTopBtn.addEventListener('click', () => this.scrollToTop());
            }

            handleScroll() {
                const scrollTop = window.pageYOffset;

                // Go to top button
                if (scrollTop > 300) {
                    this.goTopBtn.classList.remove('opacity-0', 'invisible');
                    this.goTopBtn.classList.add('opacity-100', 'visible');
                } else {
                    this.goTopBtn.classList.add('opacity-0', 'invisible');
                    this.goTopBtn.classList.remove('opacity-100', 'visible');
                }
            }

            scrollToTop() {
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }
        }

        // Initialize everything when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            // Hide loader
            const loader = document.getElementById('loader');
            setTimeout(() => {
                loader.style.opacity = '0';
                setTimeout(() => loader.style.display = 'none', 500);
            }, 1000);

            // Initialize components
            new MobileMenu();
            new ScrollEffects();
        });
    </script>
</body>
</html>
