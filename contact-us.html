<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us | Get Quote for Manufacturing Services - SUYASH</title>

    <!-- SEO Meta Tags -->
    <meta name="description" content="Contact SUYASH Manufacturing for precision engineering solutions. Get quotes, visit our facilities in Navi Mumbai and Pune, or discuss your manufacturing requirements with our expert team.">
    <meta name="keywords" content="contact manufacturing company, get quote, precision engineering, Navi Mumbai, Pune, manufacturing facilities, customer service">
    <meta name="author" content="SUYASH Manufacturing">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="https://www.suyashmanufacturing.com/contact-us.html">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://www.suyashmanufacturing.com/contact-us.html">
    <meta property="og:title" content="Contact Us | Get Quote for Manufacturing Services - SUYASH">
    <meta property="og:description" content="Contact SUYASH Manufacturing for precision engineering solutions. Get quotes, visit our facilities in Navi Mumbai and Pune.">
    <meta property="og:image" content="https://www.suyashmanufacturing.com/images/logo.png">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary">
    <meta property="twitter:url" content="https://www.suyashmanufacturing.com/contact-us.html">
    <meta property="twitter:title" content="Contact Us | Get Quote for Manufacturing Services - SUYASH">
    <meta property="twitter:description" content="Contact SUYASH Manufacturing for precision engineering solutions. Get quotes, visit our facilities in Navi Mumbai and Pune.">
    <meta property="twitter:image" content="https://www.suyashmanufacturing.com/images/logo.png">

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "ContactPage",
        "name": "Contact SUYASH Manufacturing",
        "description": "Contact page for SUYASH Manufacturing - precision engineering company",
        "url": "https://www.suyashmanufacturing.com/contact-us.html"
    }
    </script>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Custom Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#f26722',
                        secondary: '#0a0a0a',
                        accent: '#e05310',
                        gray: {
                            50: '#f9fafb',
                            100: '#f3f4f6',
                            200: '#e5e7eb',
                            300: '#d1d5db',
                            400: '#9ca3af',
                            500: '#6b7280',
                            600: '#4b5563',
                            700: '#374151',
                            800: '#1f2937',
                            900: '#111827',
                        }
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                        'display': ['Poppins', 'system-ui', 'sans-serif'],
                    },
                    animation: {
                        'fade-in-up': 'fadeInUp 0.6s ease-out',
                        'fade-in-down': 'fadeInDown 0.6s ease-out',
                        'fade-in-left': 'fadeInLeft 0.6s ease-out',
                        'fade-in-right': 'fadeInRight 0.6s ease-out',
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'bounce-slow': 'bounce 2s infinite',
                    },
                    keyframes: {
                        fadeInUp: {
                            '0%': { opacity: '0', transform: 'translateY(30px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        },
                        fadeInDown: {
                            '0%': { opacity: '0', transform: 'translateY(-30px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        },
                        fadeInLeft: {
                            '0%': { opacity: '0', transform: 'translateX(-30px)' },
                            '100%': { opacity: '1', transform: 'translateX(0)' }
                        },
                        fadeInRight: {
                            '0%': { opacity: '0', transform: 'translateX(30px)' },
                            '100%': { opacity: '1', transform: 'translateX(0)' }
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="font-sans text-gray-900 bg-white">
    <!-- Loading Screen -->
    <div id="loader" class="fixed inset-0 z-50 flex items-center justify-center bg-white transition-opacity duration-500">
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-4 border-primary border-t-transparent"></div>
            <p class="mt-4 text-gray-600 font-medium">Loading Excellence...</p>
        </div>
    </div>

    <!-- Navigation Header -->
    <header id="header" class="fixed top-0 left-0 right-0 z-40 bg-white/95 backdrop-blur-md border-b border-gray-100 transition-all duration-300">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-20">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <a href="index.html" class="flex items-center space-x-3">
                        <img src="images/logo.png" alt="SUYASH" class="h-12 w-auto transition-transform duration-300 hover:scale-105" />

                    </a>
                </div>
                
                <!-- Desktop Navigation -->
                <nav class="hidden lg:flex items-center space-x-8">
                    <a href="index.html" class="text-gray-700 hover:text-primary transition-colors duration-200 font-medium">Home</a>
                    <div class="relative group">
                        <button class="flex items-center space-x-1 text-gray-700 hover:text-primary transition-colors duration-200 font-medium">
                            <span>About</span>
                            <svg class="w-4 h-4 transition-transform duration-200 group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div class="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-100 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                            <div class="py-2">
                                <a href="history.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors duration-200">History</a>
                                <a href="locations.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors duration-200">Locations</a>
                                <a href="csr-activities.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors duration-200">CSR Activities</a>
                            </div>
                        </div>
                    </div>
                    <a href="services.html" class="text-gray-700 hover:text-primary transition-colors duration-200 font-medium">Services</a>
                    <a href="quality.html" class="text-gray-700 hover:text-primary transition-colors duration-200 font-medium">Quality</a>
                    <a href="careers.html" class="text-gray-700 hover:text-primary transition-colors duration-200 font-medium">Careers</a>
                    <a href="events.html" class="text-gray-700 hover:text-primary transition-colors duration-200 font-medium">Events</a>
                    <a href="contact-us.html" class="bg-primary text-white px-6 py-2 rounded-full hover:bg-accent transition-colors duration-200 font-medium">Contact Us</a>
                </nav>
                
                <!-- Mobile Menu Button -->
                <button id="mobileMenuToggle" class="lg:hidden flex flex-col items-center justify-center w-8 h-8 space-y-1.5">
                    <span class="w-6 h-0.5 bg-gray-700 transition-all duration-300"></span>
                    <span class="w-6 h-0.5 bg-gray-700 transition-all duration-300"></span>
                    <span class="w-6 h-0.5 bg-gray-700 transition-all duration-300"></span>
                </button>
            </div>
        </div>
        
        <!-- Mobile Navigation Menu -->
        <div id="mobileMenu" class="lg:hidden fixed inset-x-0 top-20 bg-white border-b border-gray-100 shadow-lg transform -translate-y-full opacity-0 transition-all duration-300">
            <div class="px-4 py-6 space-y-4">
                <a href="index.html" class="block text-gray-700 hover:text-primary transition-colors duration-200">Home</a>
                <div class="space-y-2">
                    <p class="text-gray-900 font-medium">About</p>
                    <div class="pl-4 space-y-2">
                        <a href="history.html" class="block text-gray-600 hover:text-primary transition-colors duration-200">History</a>
                        <a href="locations.html" class="block text-gray-600 hover:text-primary transition-colors duration-200">Locations</a>
                        <a href="csr-activities.html" class="block text-gray-600 hover:text-primary transition-colors duration-200">CSR Activities</a>
                    </div>
                </div>
                <a href="services.html" class="block text-gray-700 hover:text-primary transition-colors duration-200">Services</a>
                <a href="quality.html" class="block text-gray-700 hover:text-primary transition-colors duration-200">Quality</a>
                <a href="careers.html" class="block text-gray-700 hover:text-primary transition-colors duration-200">Careers</a>
                <a href="events.html" class="block text-gray-700 hover:text-primary transition-colors duration-200">Events</a>
                <a href="contact-us.html" class="block bg-primary text-white px-6 py-3 rounded-full text-center hover:bg-accent transition-colors duration-200">Contact Us</a>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="relative bg-gradient-to-br from-gray-900 to-gray-800 text-white pt-32 pb-20">
        <div class="absolute inset-0 bg-black/20"></div>
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-5xl lg:text-6xl font-bold mb-6">
                Contact <span class="text-primary">Us</span>
            </h1>
            <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                Get in touch with our team for your manufacturing needs
            </p>
        </div>
    </section>

    <!-- Contact Form & Info Section -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid lg:grid-cols-2 gap-16">
                <!-- Contact Form -->
                <div>
                    <div class="mb-8">
                        <span class="inline-block px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-semibold mb-4">
                            Send Message
                        </span>
                        <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">Get In Touch</h2>
                        <p class="text-lg text-gray-600">
                            Ready to discuss your manufacturing requirements? Send us a message and we'll get back to you promptly.
                        </p>
                    </div>
                    
                    <form class="space-y-6" id="contactForm">
                        <div class="grid md:grid-cols-2 gap-6">
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Name *</label>
                                <input type="text" id="name" name="name" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200">
                            </div>
                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email *</label>
                                <input type="email" id="email" name="email" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200">
                            </div>
                        </div>
                        
                        <div>
                            <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Telephone</label>
                            <input type="tel" id="phone" name="phone" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200">
                        </div>
                        
                        <div>
                            <label for="message" class="block text-sm font-medium text-gray-700 mb-2">Your Message *</label>
                            <textarea id="message" name="message" rows="6" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors duration-200 resize-none"></textarea>
                        </div>
                        
                        <button type="submit" class="w-full bg-primary hover:bg-accent text-white font-semibold py-4 px-8 rounded-lg transition-colors duration-200 transform hover:scale-105">
                            SEND MESSAGE
                        </button>
                    </form>
                </div>
                
                <!-- Contact Information -->
                <div>
                    <div class="mb-8">
                        <span class="inline-block px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-semibold mb-4">
                            Contact Information
                        </span>
                        <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">Reach Out To Us</h2>
                    </div>
                    
                    <!-- Contact Cards -->
                    <div class="space-y-6">
                        <!-- Head Office -->
                        <div class="bg-gray-50 rounded-2xl p-6 hover:shadow-lg transition-shadow duration-300">
                            <div class="flex items-start space-x-4">
                                <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                                    <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Head Office & Plant 1</h3>
                                    <p class="text-gray-600">
                                        Mahape, Navi Mumbai<br>
                                        Floor Area - 15,000 sq.ft.
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Plant 2 -->
                        <div class="bg-gray-50 rounded-2xl p-6 hover:shadow-lg transition-shadow duration-300">
                            <div class="flex items-start space-x-4">
                                <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                                    <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Plant 2</h3>
                                    <p class="text-gray-600">
                                        Turbhe, Navi Mumbai<br>
                                        Floor Area - 4,000 sq.ft.
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Pune Plants -->
                        <div class="bg-gray-50 rounded-2xl p-6 hover:shadow-lg transition-shadow duration-300">
                            <div class="flex items-start space-x-4">
                                <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                                    <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Pune Facilities</h3>
                                    <p class="text-gray-600 mb-2">
                                        Pune Plant 1 - Bhosari M.I.D.C.<br>
                                        Floor Area - 20,000 sq.ft.
                                    </p>
                                    <p class="text-gray-600">
                                        Pune Plant 2 - Chakan<br>
                                        Floor Area - 25,000 sq.ft.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Google Maps Section -->
    <section class="py-0 bg-gray-100">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-8 pt-16">
                <span class="inline-block px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-semibold mb-4">
                    Find Us
                </span>
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">Our Locations</h2>
                <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                    Visit our manufacturing facilities across Maharashtra
                </p>
            </div>
        </div>

        <!-- Map Container -->
        <div class="w-full h-96 bg-gray-300 relative overflow-hidden">
            <iframe
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3769.8234567890123!2d73.0123456789!3d19.1234567890!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMTnCsDA3JzI0LjQiTiA3M8KwMDAnNDQuNCJF!5e0!3m2!1sen!2sin!4v1234567890123!5m2!1sen!2sin"
                width="100%"
                height="100%"
                style="border:0;"
                allowfullscreen=""
                loading="lazy"
                referrerpolicy="no-referrer-when-downgrade"
                class="w-full h-full">
            </iframe>

            <!-- Map Overlay with Location Cards -->
            <div class="absolute top-4 left-4 right-4 z-10">
                <div class="grid md:grid-cols-2 gap-4">
                    <!-- Navi Mumbai Location -->
                    <div class="bg-white/95 backdrop-blur-sm rounded-lg p-4 shadow-lg">
                        <h3 class="font-semibold text-gray-900 mb-2">Navi Mumbai Facilities</h3>
                        <p class="text-sm text-gray-600 mb-1">Head Office & Plant 1: Mahape</p>
                        <p class="text-sm text-gray-600">Plant 2: Turbhe</p>
                    </div>

                    <!-- Pune Location -->
                    <div class="bg-white/95 backdrop-blur-sm rounded-lg p-4 shadow-lg">
                        <h3 class="font-semibold text-gray-900 mb-2">Pune Facilities</h3>
                        <p class="text-sm text-gray-600 mb-1">Plant 1: Bhosari M.I.D.C.</p>
                        <p class="text-sm text-gray-600">Plant 2: Chakan</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Location Quick Links -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="grid md:grid-cols-4 gap-4">
                <div class="text-center">
                    <div class="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-3">
                        <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                    </div>
                    <h4 class="font-semibold text-gray-900 text-sm">Mahape HQ</h4>
                    <p class="text-xs text-gray-600">15,000 sq.ft.</p>
                </div>

                <div class="text-center">
                    <div class="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-3">
                        <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                    </div>
                    <h4 class="font-semibold text-gray-900 text-sm">Turbhe Plant</h4>
                    <p class="text-xs text-gray-600">4,000 sq.ft.</p>
                </div>

                <div class="text-center">
                    <div class="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-3">
                        <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                    </div>
                    <h4 class="font-semibold text-gray-900 text-sm">Pune Bhosari</h4>
                    <p class="text-xs text-gray-600">20,000 sq.ft.</p>
                </div>

                <div class="text-center">
                    <div class="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-3">
                        <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                    </div>
                    <h4 class="font-semibold text-gray-900 text-sm">Pune Chakan</h4>
                    <p class="text-xs text-gray-600">25,000 sq.ft.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gradient-to-br from-gray-900 to-black text-white">
        <!-- Main Footer Content -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <div class="grid lg:grid-cols-4 md:grid-cols-2 gap-8">
                <!-- Company Info -->
                <div class="lg:col-span-2">
                    <div class="flex items-center space-x-3 mb-6">
                        <img src="images/logo.png" alt="SUYASH" class="h-12 w-auto" />

                    </div>
                    <p class="text-gray-300 leading-relaxed mb-6 max-w-md">
                        Leading manufacturer of precision engineering components, serving global markets with innovative solutions and uncompromising quality.
                    </p>
                    <div class="flex space-x-4">
                        <!-- <a href="#" class="w-10 h-10 bg-primary/20 hover:bg-primary rounded-full flex items-center justify-center transition-colors duration-300">
                            <img src="images/f-icon.png" alt="Facebook" class="w-5 h-5">
                        </a> -->
                        <a href="https://www.linkedin.com/company/suyash-impex-pvt-ltd/" class="w-10 h-10 bg-primary/20 hover:bg-primary rounded-full flex items-center justify-center transition-colors duration-300">
                            <img src="images/in-icon.png" alt="LinkedIn" class="w-5 h-5">
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="text-lg font-semibold mb-6">Quick Links</h4>
                    <ul class="space-y-3">
                        <li><a href="index.html" class="text-gray-300 hover:text-primary transition-colors duration-200">Home</a></li>
                        <li><a href="history.html" class="text-gray-300 hover:text-primary transition-colors duration-200">History</a></li>
                        <li><a href="services.html" class="text-gray-300 hover:text-primary transition-colors duration-200">Services</a></li>
                        <li><a href="quality.html" class="text-gray-300 hover:text-primary transition-colors duration-200">Quality</a></li>
                        <li><a href="careers.html" class="text-gray-300 hover:text-primary transition-colors duration-200">Careers</a></li>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div>
                    <h4 class="text-lg font-semibold mb-6">Get In Touch</h4>
                    <ul class="space-y-3">
                        <li><a href="events.html" class="text-gray-300 hover:text-primary transition-colors duration-200">Events</a></li>
                        <li><a href="contact-us.html" class="text-primary font-medium">Contact Us</a></li>
                        <li><a href="locations.html" class="text-gray-300 hover:text-primary transition-colors duration-200">Our Locations</a></li>
                        <li><a href="csr-activities.html" class="text-gray-300 hover:text-primary transition-colors duration-200">CSR Activities</a></li>
                    </ul>

                    <!-- CTA Button -->
                    <div class="mt-6">
                        <a href="contact-us.html" class="inline-flex items-center bg-primary hover:bg-accent text-white px-6 py-3 rounded-full font-semibold transition-all duration-300 transform hover:scale-105">
                            Start Your Project
                            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer Bottom -->
        <div class="border-t border-gray-800">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-gray-400 text-sm">
                        All Right Reserved Suyash 2015 | Developed by : <a href="https://www.linkedin.com/in/shyam-varma/" class="text-primary hover:text-accent transition-colors duration-200">SV</a>
                    </p>
                    <div class="flex items-center space-x-6 mt-4 md:mt-0">
                        <span class="text-gray-400 text-sm">Follow us</span>
                        <div class="flex space-x-3">
                            <!-- <a href="#" class="text-gray-400 hover:text-primary transition-colors duration-200">
                                <img src="images/f-icon.png" alt="Facebook" class="w-4 h-4">
                            </a> -->
                            <a href="https://www.linkedin.com/company/suyash-impex-pvt-ltd/" class="text-gray-400 hover:text-primary transition-colors duration-200">
                                <img src="images/in-icon.png" alt="LinkedIn" class="w-4 h-4">
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scroll to Top Button -->
    <button id="goTop" class="fixed bottom-8 right-8 w-12 h-12 bg-primary hover:bg-accent text-white rounded-full shadow-lg opacity-0 invisible transition-all duration-300 transform hover:scale-110 z-50">
        <svg class="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
        </svg>
    </button>

    <!-- JavaScript -->
    <script>
        // Mobile Menu Toggle
        class MobileMenu {
            constructor() {
                this.toggle = document.getElementById('mobileMenuToggle');
                this.menu = document.getElementById('mobileMenu');
                this.isOpen = false;
                this.init();
            }

            init() {
                this.toggle.addEventListener('click', () => this.toggleMenu());
                document.addEventListener('click', (e) => {
                    if (!this.toggle.contains(e.target) && !this.menu.contains(e.target)) {
                        this.closeMenu();
                    }
                });
            }

            toggleMenu() {
                this.isOpen = !this.isOpen;
                this.updateMenu();
            }

            closeMenu() {
                this.isOpen = false;
                this.updateMenu();
            }

            updateMenu() {
                const spans = this.toggle.querySelectorAll('span');
                if (this.isOpen) {
                    this.menu.style.transform = 'translateY(0)';
                    this.menu.style.opacity = '1';
                    spans[0].style.transform = 'rotate(45deg) translate(5px, 5px)';
                    spans[1].style.opacity = '0';
                    spans[2].style.transform = 'rotate(-45deg) translate(7px, -6px)';
                } else {
                    this.menu.style.transform = 'translateY(-100%)';
                    this.menu.style.opacity = '0';
                    spans.forEach(span => {
                        span.style.transform = 'none';
                        span.style.opacity = '1';
                    });
                }
            }
        }

        // Contact Form Handler
        class ContactForm {
            constructor() {
                this.form = document.getElementById('contactForm');
                this.init();
            }

            init() {
                this.form.addEventListener('submit', (e) => this.handleSubmit(e));
            }

            handleSubmit(e) {
                e.preventDefault();

                // Get form data
                const formData = new FormData(this.form);
                const data = Object.fromEntries(formData);

                // Simple validation
                if (!data.name || !data.email || !data.message) {
                    alert('Please fill in all required fields.');
                    return;
                }

                // Simulate form submission
                const submitBtn = this.form.querySelector('button[type="submit"]');
                const originalText = submitBtn.textContent;

                submitBtn.textContent = 'SENDING...';
                submitBtn.disabled = true;

                setTimeout(() => {
                    alert('Thank you for your message! We will get back to you soon.');
                    this.form.reset();
                    submitBtn.textContent = originalText;
                    submitBtn.disabled = false;
                }, 2000);
            }
        }

        // Scroll Effects
        class ScrollEffects {
            constructor() {
                this.header = document.getElementById('header');
                this.goTopBtn = document.getElementById('goTop');
                this.init();
            }

            init() {
                window.addEventListener('scroll', () => this.handleScroll());
                this.goTopBtn.addEventListener('click', () => this.scrollToTop());
            }

            handleScroll() {
                const scrollTop = window.pageYOffset;

                // Go to top button
                if (scrollTop > 300) {
                    this.goTopBtn.classList.remove('opacity-0', 'invisible');
                    this.goTopBtn.classList.add('opacity-100', 'visible');
                } else {
                    this.goTopBtn.classList.add('opacity-0', 'invisible');
                    this.goTopBtn.classList.remove('opacity-100', 'visible');
                }
            }

            scrollToTop() {
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }
        }

        // Initialize everything when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            // Hide loader
            const loader = document.getElementById('loader');
            setTimeout(() => {
                loader.style.opacity = '0';
                setTimeout(() => loader.style.display = 'none', 500);
            }, 1000);

            // Initialize components
            new MobileMenu();
            new ContactForm();
            new ScrollEffects();
        });
    </script>
</body>
</html>
