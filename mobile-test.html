<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Navigation Test - SUYASH</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#f26722',
                        secondary: '#0a0a0a',
                        accent: '#e05310',
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-100">
    <div class="max-w-4xl mx-auto p-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">Mobile Navigation Test</h1>
        
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4">Test Instructions</h2>
            <ol class="list-decimal list-inside space-y-2 text-gray-700">
                <li>Open this page on a mobile device or resize browser to mobile width (&lt;1024px)</li>
                <li>Look for the hamburger menu (three lines) in the top navigation</li>
                <li>Tap the hamburger menu - it should slide in a sidebar from the left</li>
                <li>Verify the sidebar contains:
                    <ul class="list-disc list-inside ml-6 mt-2 space-y-1">
                        <li>SUYASH logo and company name at the top</li>
                        <li>Close button (X) in the top right</li>
                        <li>Navigation menu with icons</li>
                        <li>About dropdown that expands/collapses</li>
                        <li>Contact Us button at the bottom</li>
                        <li>LinkedIn social media icon</li>
                    </ul>
                </li>
                <li>Test closing the menu by:
                    <ul class="list-disc list-inside ml-6 mt-2 space-y-1">
                        <li>Tapping the X button</li>
                        <li>Tapping the dark overlay area</li>
                        <li>Pressing the Escape key (on desktop)</li>
                    </ul>
                </li>
                <li>Verify the hamburger icon animates to an X when open</li>
                <li>Test the About dropdown functionality</li>
            </ol>
        </div>
        
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4">Pages Updated</h2>
            <div class="grid md:grid-cols-2 gap-4">
                <div>
                    <h3 class="font-semibold text-green-600 mb-2">✅ Completed</h3>
                    <ul class="space-y-1 text-sm">
                        <li>• index.html</li>
                        <li>• careers.html</li>
                        <li>• services.html</li>
                    </ul>
                </div>
                <div>
                    <h3 class="font-semibold text-orange-600 mb-2">🔄 Pending</h3>
                    <ul class="space-y-1 text-sm">
                        <li>• contact-us.html</li>
                        <li>• quality.html</li>
                        <li>• history.html</li>
                        <li>• locations.html</li>
                        <li>• csr-activities.html</li>
                        <li>• events.html</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-semibold mb-4">SEO Files Created</h2>
            <div class="grid md:grid-cols-3 gap-4">
                <div class="text-center p-4 bg-green-50 rounded-lg">
                    <div class="text-2xl mb-2">📄</div>
                    <h3 class="font-semibold text-green-800">robots.txt</h3>
                    <p class="text-sm text-green-600">Search engine directives</p>
                </div>
                <div class="text-center p-4 bg-blue-50 rounded-lg">
                    <div class="text-2xl mb-2">🗺️</div>
                    <h3 class="font-semibold text-blue-800">sitemap.xml</h3>
                    <p class="text-sm text-blue-600">Site structure for crawlers</p>
                </div>
                <div class="text-center p-4 bg-purple-50 rounded-lg">
                    <div class="text-2xl mb-2">🤖</div>
                    <h3 class="font-semibold text-purple-800">ai.txt</h3>
                    <p class="text-sm text-purple-600">AI/LLM access guidelines</p>
                </div>
            </div>
        </div>
        
        <div class="mt-8 text-center">
            <a href="index.html" class="inline-flex items-center bg-primary hover:bg-accent text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200">
                ← Back to Homepage
            </a>
        </div>
    </div>
</body>
</html>
