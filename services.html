<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manufacturing Services | Metal Cutting, Forming & Assemblies - SUYASH</title>

    <!-- SEO Meta Tags -->
    <meta name="description" content="Comprehensive manufacturing services including metal cutting, metal forming, and assemblies. One-stop solution for precision engineering components with advanced processes and quality assurance.">
    <meta name="keywords" content="metal cutting, metal forming, assemblies, turning, milling, grinding, punching, bending, deep drawing, precision manufacturing, engineering services">
    <meta name="author" content="SUYASH Manufacturing">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="https://www.suyashmanufacturing.com/services.html">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://www.suyashmanufacturing.com/services.html">
    <meta property="og:title" content="Manufacturing Services | Metal Cutting, Forming & Assemblies - SUYASH">
    <meta property="og:description" content="Comprehensive manufacturing services including metal cutting, metal forming, and assemblies. One-stop solution for precision engineering components.">
    <meta property="og:image" content="https://www.suyashmanufacturing.com/images/service1.jpg">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://www.suyashmanufacturing.com/services.html">
    <meta property="twitter:title" content="Manufacturing Services | Metal Cutting, Forming & Assemblies - SUYASH">
    <meta property="twitter:description" content="Comprehensive manufacturing services including metal cutting, metal forming, and assemblies. One-stop solution for precision engineering components.">
    <meta property="twitter:image" content="https://www.suyashmanufacturing.com/images/service1.jpg">

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Service",
        "name": "Manufacturing Services",
        "provider": {
            "@type": "Organization",
            "name": "SUYASH Manufacturing"
        },
        "description": "Comprehensive manufacturing services including metal cutting, metal forming, and assemblies",
        "serviceType": ["Metal Cutting", "Metal Forming", "Assemblies"],
        "areaServed": "Global"
    }
    </script>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Custom Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#f26722',
                        secondary: '#0a0a0a',
                        accent: '#e05310',
                        gray: {
                            50: '#f9fafb',
                            100: '#f3f4f6',
                            200: '#e5e7eb',
                            300: '#d1d5db',
                            400: '#9ca3af',
                            500: '#6b7280',
                            600: '#4b5563',
                            700: '#374151',
                            800: '#1f2937',
                            900: '#111827',
                        }
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                        'display': ['Poppins', 'system-ui', 'sans-serif'],
                    },
                    animation: {
                        'fade-in-up': 'fadeInUp 0.6s ease-out',
                        'fade-in-down': 'fadeInDown 0.6s ease-out',
                        'fade-in-left': 'fadeInLeft 0.6s ease-out',
                        'fade-in-right': 'fadeInRight 0.6s ease-out',
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'bounce-slow': 'bounce 2s infinite',
                    },
                    keyframes: {
                        fadeInUp: {
                            '0%': { opacity: '0', transform: 'translateY(30px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        },
                        fadeInDown: {
                            '0%': { opacity: '0', transform: 'translateY(-30px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        },
                        fadeInLeft: {
                            '0%': { opacity: '0', transform: 'translateX(-30px)' },
                            '100%': { opacity: '1', transform: 'translateX(0)' }
                        },
                        fadeInRight: {
                            '0%': { opacity: '0', transform: 'translateX(30px)' },
                            '100%': { opacity: '1', transform: 'translateX(0)' }
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="font-sans text-gray-900 bg-white">
    <!-- Loading Screen -->
    <div id="loader" class="fixed inset-0 z-50 flex items-center justify-center bg-white transition-opacity duration-500">
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-4 border-primary border-t-transparent"></div>
            <p class="mt-4 text-gray-600 font-medium">Loading Excellence...</p>
        </div>
    </div>

    <!-- Navigation Header -->
    <header id="header" class="fixed top-0 left-0 right-0 z-40 bg-white/95 backdrop-blur-md border-b border-gray-100 transition-all duration-300">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-20">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <a href="index.html" class="flex items-center space-x-3">
                        <img src="images/logo.png" alt="SUYASH" class="h-12 w-auto transition-transform duration-300 hover:scale-105" />

                    </a>
                </div>
                
                <!-- Desktop Navigation -->
                <nav class="hidden lg:flex items-center space-x-8">
                    <a href="index.html" class="text-gray-700 hover:text-primary transition-colors duration-200 font-medium">Home</a>
                    <div class="relative group">
                        <button class="flex items-center space-x-1 text-gray-700 hover:text-primary transition-colors duration-200 font-medium">
                            <span>About</span>
                            <svg class="w-4 h-4 transition-transform duration-200 group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div class="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-100 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                            <div class="py-2">
                                <a href="history.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors duration-200">History</a>
                                <a href="locations.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors duration-200">Locations</a>
                                <a href="csr-activities.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors duration-200">CSR Activities</a>
                            </div>
                        </div>
                    </div>
                    <a href="services.html" class="text-primary font-semibold border-b-2 border-primary pb-1">Services</a>
                    <a href="quality.html" class="text-gray-700 hover:text-primary transition-colors duration-200 font-medium">Quality</a>
                    <a href="careers.html" class="text-gray-700 hover:text-primary transition-colors duration-200 font-medium">Careers</a>
                    <a href="events.html" class="text-gray-700 hover:text-primary transition-colors duration-200 font-medium">Events</a>
                    <a href="contact-us.html" class="bg-primary text-white px-6 py-2 rounded-full hover:bg-accent transition-colors duration-200 font-medium">Contact Us</a>
                </nav>
                
                <!-- Mobile Menu Button -->
                <button id="mobileMenuToggle" class="lg:hidden flex flex-col items-center justify-center w-8 h-8 space-y-1.5">
                    <span class="w-6 h-0.5 bg-gray-700 transition-all duration-300"></span>
                    <span class="w-6 h-0.5 bg-gray-700 transition-all duration-300"></span>
                    <span class="w-6 h-0.5 bg-gray-700 transition-all duration-300"></span>
                </button>
            </div>
        </div>
        
        <!-- Mobile Navigation Menu -->
        <div id="mobileMenu" class="lg:hidden fixed inset-x-0 top-20 bg-white border-b border-gray-100 shadow-lg transform -translate-y-full opacity-0 transition-all duration-300">
            <div class="px-4 py-6 space-y-4">
                <a href="index.html" class="block text-gray-700 hover:text-primary transition-colors duration-200">Home</a>
                <div class="space-y-2">
                    <p class="text-gray-900 font-medium">About</p>
                    <div class="pl-4 space-y-2">
                        <a href="history.html" class="block text-gray-600 hover:text-primary transition-colors duration-200">History</a>
                        <a href="locations.html" class="block text-gray-600 hover:text-primary transition-colors duration-200">Locations</a>
                        <a href="csr-activities.html" class="block text-gray-600 hover:text-primary transition-colors duration-200">CSR Activities</a>
                    </div>
                </div>
                <a href="services.html" class="block text-primary font-semibold">Services</a>
                <a href="quality.html" class="block text-gray-700 hover:text-primary transition-colors duration-200">Quality</a>
                <a href="careers.html" class="block text-gray-700 hover:text-primary transition-colors duration-200">Careers</a>
                <a href="events.html" class="block text-gray-700 hover:text-primary transition-colors duration-200">Events</a>
                <a href="contact-us.html" class="block bg-primary text-white px-6 py-3 rounded-full text-center hover:bg-accent transition-colors duration-200">Contact Us</a>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="relative bg-gradient-to-br from-gray-900 to-gray-800 text-white pt-32 pb-20">
        <div class="absolute inset-0 bg-black/20"></div>
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-5xl lg:text-6xl font-bold mb-6">
                Our <span class="text-primary">Services</span>
            </h1>
            <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                Comprehensive manufacturing solutions for precision engineering components
            </p>
        </div>
    </section>

    <!-- Services Overview Section -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="max-w-4xl mx-auto text-center mb-16">
                <span class="inline-block px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-semibold mb-4">
                    What We Offer
                </span>
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">Complete Manufacturing Solutions</h2>
                <p class="text-lg text-gray-600 leading-relaxed">
                    Suyash today prides itself in the fact that it is one of the very few engineering outsourcing companies offering a one-stop manufacturing solution. Though the processes of metal cutting and metal forming need very different competencies; we offer both these processes under the same roof. We handle conventional metal cutting processes like turning, milling, centreless grinding, as well as special processes like thread rolling, lapping, spin riveting, etc. On the metal forming front, we offer punching, bending, nibbling, notching & deep drawing for sheets upto 6 mm thickness.
                </p>
            </div>
        </div>
    </section>

    <!-- Detailed Services Section -->
    <section class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid lg:grid-cols-3 gap-8">
                <!-- Metal Forming Service -->
                <div class="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300">
                    <div class="aspect-w-16 aspect-h-12">
                        <img src="images/service1.jpg" alt="Metal Forming" class="w-full h-64 object-cover">
                    </div>
                    <div class="p-8">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mr-4">
                                <img src="images/1_Metal-Forming.png" alt="Metal Forming Icon" class="w-8 h-8">
                            </div>
                            <h3 class="text-2xl font-bold text-gray-900">Metal Forming</h3>
                        </div>
                        <p class="text-gray-600 leading-relaxed mb-6">
                            Over 3 decades of manufacturing a variety of sheet metal components and fabrication, Suyash today is in a position to become a reliable partner for sheet metal components & assemblies.
                        </p>

                        <!-- Capabilities -->
                        <div class="space-y-3 mb-6">
                            <h4 class="font-semibold text-gray-900 mb-3">Capabilities:</h4>
                            <div class="flex items-center space-x-3">
                                <div class="w-2 h-2 bg-primary rounded-full"></div>
                                <span class="text-sm text-gray-600">Punching & Bending</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <div class="w-2 h-2 bg-primary rounded-full"></div>
                                <span class="text-sm text-gray-600">Nibbling & Notching</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <div class="w-2 h-2 bg-primary rounded-full"></div>
                                <span class="text-sm text-gray-600">Deep Drawing (up to 6mm)</span>
                            </div>
                        </div>

                        <a href="contact-us.html" class="inline-flex items-center text-primary hover:text-accent font-medium transition-colors duration-200">
                            Learn More
                            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Metal Cutting Service -->
                <div class="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300">
                    <div class="aspect-w-16 aspect-h-12">
                        <img src="images/service2.jpg" alt="Metal Cutting" class="w-full h-64 object-cover">
                    </div>
                    <div class="p-8">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mr-4">
                                <img src="images/2_Metal-Cutting.png" alt="Metal Cutting Icon" class="w-8 h-8">
                            </div>
                            <h3 class="text-2xl font-bold text-gray-900">Metal Cutting</h3>
                        </div>
                        <p class="text-gray-600 leading-relaxed mb-6">
                            Armed with the experience of producing precision components by using a variety of metal cutting processes like turning, milling, grinding, super finishing, etc, we have developed a fully-equipped machine shop to handle virtually all processes needed to produce precision machined components.
                        </p>

                        <!-- Capabilities -->
                        <div class="space-y-3 mb-6">
                            <h4 class="font-semibold text-gray-900 mb-3">Capabilities:</h4>
                            <div class="flex items-center space-x-3">
                                <div class="w-2 h-2 bg-primary rounded-full"></div>
                                <span class="text-sm text-gray-600">Turning & Milling</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <div class="w-2 h-2 bg-primary rounded-full"></div>
                                <span class="text-sm text-gray-600">Centreless Grinding</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <div class="w-2 h-2 bg-primary rounded-full"></div>
                                <span class="text-sm text-gray-600">Thread Rolling & Lapping</span>
                            </div>
                        </div>

                        <a href="contact-us.html" class="inline-flex items-center text-primary hover:text-accent font-medium transition-colors duration-200">
                            Learn More
                            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Assemblies Service -->
                <div class="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300">
                    <div class="aspect-w-16 aspect-h-12">
                        <img src="images/service3.jpg" alt="Assemblies" class="w-full h-64 object-cover">
                    </div>
                    <div class="p-8">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mr-4">
                                <img src="images/3_Assemblies.png" alt="Assemblies Icon" class="w-8 h-8">
                            </div>
                            <h3 class="text-2xl font-bold text-gray-900">Assemblies</h3>
                        </div>
                        <p class="text-gray-600 leading-relaxed mb-6">
                            Our journey upwards in the value chain began long ago with welded assemblies supplied to the automotive sector. Today we handle small & intricate switchgear assemblies as well as large fuel containment tank assemblies with the same dexterity.
                        </p>

                        <!-- Capabilities -->
                        <div class="space-y-3 mb-6">
                            <h4 class="font-semibold text-gray-900 mb-3">Capabilities:</h4>
                            <div class="flex items-center space-x-3">
                                <div class="w-2 h-2 bg-primary rounded-full"></div>
                                <span class="text-sm text-gray-600">Welded Assemblies</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <div class="w-2 h-2 bg-primary rounded-full"></div>
                                <span class="text-sm text-gray-600">Switchgear Assemblies</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <div class="w-2 h-2 bg-primary rounded-full"></div>
                                <span class="text-sm text-gray-600">Fuel Tank Assemblies</span>
                            </div>
                        </div>

                        <a href="contact-us.html" class="inline-flex items-center text-primary hover:text-accent font-medium transition-colors duration-200">
                            Learn More
                            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Process Flow Section -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <span class="inline-block px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-semibold mb-4">
                    Our Process
                </span>
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">Manufacturing Excellence Process</h2>
                <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                    Our streamlined process ensures quality, efficiency, and precision in every project
                </p>
            </div>

            <div class="grid md:grid-cols-4 gap-8">
                <div class="text-center">
                    <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl font-bold text-primary">1</span>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Consultation</h3>
                    <p class="text-gray-600">Understanding your requirements and specifications</p>
                </div>

                <div class="text-center">
                    <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl font-bold text-primary">2</span>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Design</h3>
                    <p class="text-gray-600">Engineering solutions tailored to your needs</p>
                </div>

                <div class="text-center">
                    <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl font-bold text-primary">3</span>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Manufacturing</h3>
                    <p class="text-gray-600">Precision production using advanced techniques</p>
                </div>

                <div class="text-center">
                    <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl font-bold text-primary">4</span>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Delivery</h3>
                    <p class="text-gray-600">Quality assurance and timely delivery</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action -->
    <section class="py-20 bg-gradient-to-br from-primary to-accent">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="max-w-3xl mx-auto">
                <h2 class="text-3xl lg:text-4xl font-bold text-white mb-6">Ready to Start Your Project?</h2>
                <p class="text-xl text-white/90 mb-8">
                    Let's discuss how our comprehensive manufacturing solutions can meet your precision engineering needs.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="contact-us.html" class="inline-flex items-center bg-white text-primary px-8 py-4 rounded-full font-semibold hover:bg-gray-100 transition-colors duration-300">
                        Get Quote
                        <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                    <a href="quality.html" class="inline-flex items-center border-2 border-white text-white px-8 py-4 rounded-full font-semibold hover:bg-white hover:text-primary transition-colors duration-300">
                        View Quality Standards
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gradient-to-br from-gray-900 to-black text-white">
        <!-- Main Footer Content -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
            <div class="grid lg:grid-cols-4 md:grid-cols-2 gap-8">
                <!-- Company Info -->
                <div class="lg:col-span-2">
                    <div class="flex items-center space-x-3 mb-6">
                        <img src="images/logo.png" alt="SUYASH" class="h-12 w-auto" />

                    </div>
                    <p class="text-gray-300 leading-relaxed mb-6 max-w-md">
                        Leading manufacturer of precision engineering components, serving global markets with innovative solutions and uncompromising quality.
                    </p>
                    <div class="flex space-x-4">
                        <!-- <a href="#" class="w-10 h-10 bg-primary/20 hover:bg-primary rounded-full flex items-center justify-center transition-colors duration-300">
                            <img src="images/f-icon.png" alt="Facebook" class="w-5 h-5">
                        </a> -->
                        <a href="https://www.linkedin.com/company/suyash-impex-pvt-ltd/" class="w-10 h-10 bg-primary/20 hover:bg-primary rounded-full flex items-center justify-center transition-colors duration-300">
                            <img src="images/in-icon.png" alt="LinkedIn" class="w-5 h-5">
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="text-lg font-semibold mb-6">Quick Links</h4>
                    <ul class="space-y-3">
                        <li><a href="index.html" class="text-gray-300 hover:text-primary transition-colors duration-200">Home</a></li>
                        <li><a href="history.html" class="text-gray-300 hover:text-primary transition-colors duration-200">History</a></li>
                        <li><a href="services.html" class="text-primary font-medium">Services</a></li>
                        <li><a href="quality.html" class="text-gray-300 hover:text-primary transition-colors duration-200">Quality</a></li>
                        <li><a href="careers.html" class="text-gray-300 hover:text-primary transition-colors duration-200">Careers</a></li>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div>
                    <h4 class="text-lg font-semibold mb-6">Get In Touch</h4>
                    <ul class="space-y-3">
                        <li><a href="events.html" class="text-gray-300 hover:text-primary transition-colors duration-200">Events</a></li>
                        <li><a href="contact-us.html" class="text-gray-300 hover:text-primary transition-colors duration-200">Contact Us</a></li>
                        <li><a href="locations.html" class="text-gray-300 hover:text-primary transition-colors duration-200">Our Locations</a></li>
                        <li><a href="csr-activities.html" class="text-gray-300 hover:text-primary transition-colors duration-200">CSR Activities</a></li>
                    </ul>

                    <!-- CTA Button -->
                    <div class="mt-6">
                        <a href="contact-us.html" class="inline-flex items-center bg-primary hover:bg-accent text-white px-6 py-3 rounded-full font-semibold transition-all duration-300 transform hover:scale-105">
                            Start Your Project
                            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer Bottom -->
        <div class="border-t border-gray-800">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-gray-400 text-sm">
                        All Right Reserved Suyash 2015 | Developed by : <a href="#" class="text-primary hover:text-accent transition-colors duration-200">SV</a>
                    </p>
                    <div class="flex items-center space-x-6 mt-4 md:mt-0">
                        <span class="text-gray-400 text-sm">Follow us</span>
                        <div class="flex space-x-3">
                            <!-- <a href="#" class="text-gray-400 hover:text-primary transition-colors duration-200">
                                <img src="images/f-icon.png" alt="Facebook" class="w-4 h-4">
                            </a> -->
                            <a href="https://www.linkedin.com/company/suyash-impex-pvt-ltd/" class="text-gray-400 hover:text-primary transition-colors duration-200">
                                <img src="images/in-icon.png" alt="LinkedIn" class="w-4 h-4">
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scroll to Top Button -->
    <button id="goTop" class="fixed bottom-8 right-8 w-12 h-12 bg-primary hover:bg-accent text-white rounded-full shadow-lg opacity-0 invisible transition-all duration-300 transform hover:scale-110 z-50">
        <svg class="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
        </svg>
    </button>

    <!-- JavaScript -->
    <script>
        // Mobile Menu Toggle
        class MobileMenu {
            constructor() {
                this.toggle = document.getElementById('mobileMenuToggle');
                this.menu = document.getElementById('mobileMenu');
                this.isOpen = false;
                this.init();
            }

            init() {
                this.toggle.addEventListener('click', () => this.toggleMenu());
                document.addEventListener('click', (e) => {
                    if (!this.toggle.contains(e.target) && !this.menu.contains(e.target)) {
                        this.closeMenu();
                    }
                });
            }

            toggleMenu() {
                this.isOpen = !this.isOpen;
                this.updateMenu();
            }

            closeMenu() {
                this.isOpen = false;
                this.updateMenu();
            }

            updateMenu() {
                const spans = this.toggle.querySelectorAll('span');
                if (this.isOpen) {
                    this.menu.style.transform = 'translateY(0)';
                    this.menu.style.opacity = '1';
                    spans[0].style.transform = 'rotate(45deg) translate(5px, 5px)';
                    spans[1].style.opacity = '0';
                    spans[2].style.transform = 'rotate(-45deg) translate(7px, -6px)';
                } else {
                    this.menu.style.transform = 'translateY(-100%)';
                    this.menu.style.opacity = '0';
                    spans.forEach(span => {
                        span.style.transform = 'none';
                        span.style.opacity = '1';
                    });
                }
            }
        }

        // Scroll Effects
        class ScrollEffects {
            constructor() {
                this.header = document.getElementById('header');
                this.goTopBtn = document.getElementById('goTop');
                this.init();
            }

            init() {
                window.addEventListener('scroll', () => this.handleScroll());
                this.goTopBtn.addEventListener('click', () => this.scrollToTop());
            }

            handleScroll() {
                const scrollTop = window.pageYOffset;

                // Go to top button
                if (scrollTop > 300) {
                    this.goTopBtn.classList.remove('opacity-0', 'invisible');
                    this.goTopBtn.classList.add('opacity-100', 'visible');
                } else {
                    this.goTopBtn.classList.add('opacity-0', 'invisible');
                    this.goTopBtn.classList.remove('opacity-100', 'visible');
                }
            }

            scrollToTop() {
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }
        }

        // Initialize everything when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            // Hide loader
            const loader = document.getElementById('loader');
            setTimeout(() => {
                loader.style.opacity = '0';
                setTimeout(() => loader.style.display = 'none', 500);
            }, 1000);

            // Initialize components
            new MobileMenu();
            new ScrollEffects();
        });
    </script>
</body>
</html>
