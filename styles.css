/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #333333;
    background-color: #ffffff;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Loader */
.loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease;
}

.loader.hidden {
    opacity: 0;
    pointer-events: none;
}

.loader-content {
    text-align: center;
}

.loader-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #f26722;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Header */
.header {
    background: #ffffff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: all 0.3s ease;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
}

.logo img {
    height: 50px;
    width: auto;
}

/* Navigation */
.nav-menu {
    display: flex;
    list-style: none;
    align-items: center;
}

.nav-menu li {
    position: relative;
    margin: 0 20px;
}

.nav-menu a {
    text-decoration: none;
    color: #0a0a0a;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    transition: color 0.3s ease;
    padding: 10px 0;
    display: block;
}

.nav-menu a:hover,
.nav-menu a.active {
    color: #f26722;
}

/* Dropdown Menu */
.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: #ffffff;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    list-style: none;
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu li {
    margin: 0;
}

.dropdown-menu a {
    padding: 12px 20px;
    border-bottom: 1px solid #f0f0f0;
    text-transform: none;
    font-weight: 400;
}

.dropdown-menu a:hover {
    background: #f8f8f8;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 5px;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: #0a0a0a;
    margin: 3px 0;
    transition: 0.3s;
}

.mobile-menu-toggle.active span:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
}

.mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
}

/* Mobile Navigation */
@media (max-width: 768px) {
    .nav {
        position: fixed;
        top: 80px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 80px);
        background: #ffffff;
        transition: left 0.3s ease;
        z-index: 999;
    }

    .nav.active {
        left: 0;
    }

    .nav-menu {
        flex-direction: column;
        padding: 20px;
        height: 100%;
        overflow-y: auto;
    }

    .nav-menu li {
        margin: 10px 0;
        width: 100%;
    }

    .nav-menu a {
        padding: 15px 0;
        border-bottom: 1px solid #f0f0f0;
        font-size: 16px;
    }

    .dropdown-menu {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        background: #f8f8f8;
        margin-top: 10px;
    }
}

/* Hero Slider */
.hero-slider {
    position: relative;
    height: 70vh;
    overflow: hidden;
    margin-top: 80px;
}

.slider-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 1s ease-in-out;
}

.slide.active {
    opacity: 1;
}

.slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Slider Navigation */
.slider-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
    pointer-events: none;
}

.slider-btn {
    background: rgba(242, 103, 34, 0.8);
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    font-size: 18px;
    cursor: pointer;
    pointer-events: all;
    transition: all 0.3s ease;
}

.slider-btn:hover {
    background: #f26722;
    transform: scale(1.1);
}

/* Slider Dots */
.slider-dots {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255,255,255,0.5);
    cursor: pointer;
    transition: all 0.3s ease;
}

.dot.active {
    background: #f26722;
}

/* Section Styles */
section {
    padding: 80px 0;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #0a0a0a;
    line-height: 1.2;
}

.highlight {
    color: #f26722;
}

/* About Section */
.about-section {
    background: #f4f4f4;
    padding: 60px 0;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: center;
}

.about-video iframe {
    width: 100%;
    height: 315px;
    border-radius: 8px;
}

.about-text h2 {
    font-size: 2rem;
    font-weight: 700;
    color: #0a0a0a;
    margin-bottom: 20px;
    text-align: left;
}

.about-text h2 span {
    color: #f26722;
}

.about-text p {
    font-size: 16px;
    line-height: 1.6;
    color: #333333;
}

/* Services Section */
.services-section {
    padding: 60px 0;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.service-card {
    background: #1a1a1a;
    color: #ffffff;
    padding: 40px 30px;
    border-radius: 8px;
    text-align: center;
    transition: transform 0.3s ease;
}

.service-card:hover {
    transform: scale(1.05);
}

.service-icon {
    margin-bottom: 20px;
}

.service-icon img {
    width: 80px;
    height: 80px;
    object-fit: contain;
}

.service-card h5 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: #f26722;
}

.service-card p {
    font-size: 14px;
    line-height: 1.6;
}

.read-more-btn {
    display: inline-block;
    background: #f26722;
    color: white;
    padding: 12px 30px;
    text-decoration: none;
    border-radius: 4px;
    font-weight: 600;
    text-transform: uppercase;
    transition: all 0.3s ease;
    margin: 0 auto;
    display: block;
    width: fit-content;
}

.read-more-btn:hover {
    background: #e05310;
    transform: translateY(-2px);
}

/* Leadership Section */
.leadership-section {
    background: #f8f8f8;
    padding: 60px 0;
}

.leadership-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 50px;
    align-items: flex-start;
}

.leadership-image img {
    width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.leadership-text h2 {
    font-size: 2.2rem;
    font-weight: 700;
    color: #0a0a0a;
    margin-bottom: 30px;
}

.leader-profile {
    margin-bottom: 30px;
}

.leader-profile h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #f26722;
    margin-bottom: 10px;
}

.leader-profile p {
    font-size: 15px;
    line-height: 1.6;
    color: #333333;
}

/* Milestones Section */
.milestones-section {
    padding: 80px 0;
    background: #ffffff;
}

.milestones-timeline {
    max-width: 800px;
    margin: 0 auto;
}

.milestone-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 40px;
    padding: 20px;
    background: #f8f8f8;
    border-radius: 8px;
    transition: transform 0.3s ease;
}

.milestone-item:hover {
    transform: translateX(10px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.milestone-year {
    flex-shrink: 0;
    width: 100px;
    text-align: center;
    margin-right: 30px;
}

.milestone-year h1 {
    font-size: 2rem;
    font-weight: 700;
    color: #f26722;
    background: #ffffff;
    padding: 15px;
    border-radius: 50%;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.milestone-item blockquote {
    font-size: 15px;
    line-height: 1.6;
    color: #333333;
    font-style: italic;
    margin: 0;
    padding: 0;
    border: none;
}

/* Footer */
.footer {
    background: #0a0a0a;
    color: #ffffff;
}

.footer-social {
    padding: 40px 0;
    border-bottom: 1px solid #333;
}

.socials {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
}

.socials span {
    font-weight: 600;
    margin-right: 10px;
}

.socials ul {
    display: flex;
    list-style: none;
    gap: 15px;
}

.socials a {
    display: block;
    transition: transform 0.3s ease;
}

.socials a:hover {
    transform: scale(1.2);
}

.socials img {
    width: 30px;
    height: 30px;
}

.footer-menu {
    padding: 30px 0;
}

.footer-menu ul {
    display: flex;
    justify-content: center;
    list-style: none;
    flex-wrap: wrap;
    gap: 30px;
}

.footer-menu a {
    color: #ffffff;
    text-decoration: none;
    font-weight: 400;
    transition: color 0.3s ease;
}

.footer-menu a:hover {
    color: #f26722;
}

.footer-copy {
    background: #000000;
    padding: 20px 0;
    text-align: center;
    font-size: 14px;
    color: #888;
}

.footer-copy span {
    padding: 0 10px;
}

/* Go Top Button */
.go-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: #f26722;
    color: white;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    text-decoration: none;
    font-size: 20px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 999;
}

.go-top.visible {
    opacity: 1;
    visibility: visible;
}

.go-top:hover {
    background: #e05310;
    transform: translateY(-3px);
}

/* Responsive Design */
@media (max-width: 992px) {
    .container {
        padding: 0 15px;
    }

    .section-header h1 {
        font-size: 2rem;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .leadership-content {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .milestone-item {
        flex-direction: column;
        text-align: center;
    }

    .milestone-year {
        margin-right: 0;
        margin-bottom: 20px;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .hero-slider {
        height: 50vh;
        margin-top: 70px;
    }

    .slider-btn {
        width: 40px;
        height: 40px;
        font-size: 14px;
    }

    section {
        padding: 40px 0;
    }

    .about-section,
    .leadership-section {
        padding: 40px 0;
    }

    .about-text h2 {
        font-size: 1.5rem;
    }

    .leadership-text h2 {
        font-size: 1.8rem;
    }

    .footer-menu ul {
        flex-direction: column;
        gap: 15px;
    }
}

@media (max-width: 480px) {
    .header-content {
        padding: 10px 0;
    }

    .logo img {
        height: 40px;
    }

    .hero-slider {
        height: 40vh;
    }

    .section-header h1 {
        font-size: 1.5rem;
    }

    .service-card {
        padding: 30px 20px;
    }

    .milestone-year h1 {
        font-size: 1.5rem;
        padding: 10px;
    }
}

/* Animation Classes */
.animate {
    animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.service-card,
.milestone-item,
.about-content,
.leadership-content {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.service-card.animate,
.milestone-item.animate,
.about-content.animate,
.leadership-content.animate {
    opacity: 1;
    transform: translateY(0);
}

/* Header scroll effect */
.header.scrolled {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

/* Hover effects */
.service-card:hover {
    transform: scale(1.05) translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.2);
}

.milestone-item:hover {
    transform: translateX(10px);
    box-shadow: 0 10px 30px rgba(242, 103, 34, 0.1);
}

/* Loading states */
.loading {
    opacity: 0.7;
    pointer-events: none;
}

/* Focus styles for accessibility */
button:focus,
a:focus,
input:focus,
textarea:focus {
    outline: 2px solid #f26722;
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .header,
    .footer,
    .go-top,
    .slider-nav,
    .slider-dots {
        display: none !important;
    }

    .hero-slider {
        height: auto;
        margin-top: 0;
    }

    .slide {
        position: static;
        opacity: 1;
    }

    .slide:not(:first-child) {
        display: none;
    }
}
